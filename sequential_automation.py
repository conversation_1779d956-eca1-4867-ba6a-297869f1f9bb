#!/usr/bin/env python3
"""
顺序自动化任务执行器
按照配置文件中的浏览器顺序，逐个执行自动化任务
每个浏览器完成后再执行下一个，scroll_count 在 50-100 次之间随机
"""

import asyncio
import random
import logging
import sys
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.browser_api import ixBrowserAPI
from src.youtube_bot import YouTubeBot
from utils.config_manager import ConfigManager
from utils.console_logger import ConsoleLoggerSetup


class SequentialAutomation:
    """顺序自动化任务执行器"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.browser_api = ixBrowserAPI()
        self.logger = None
        self.total_stats = {
            "browsers_completed": 0,
            "total_videos_watched": 0,
            "total_likes_given": 0,
            "total_subscriptions_made": 0,
            "total_errors": 0,
            "start_time": None,
            "end_time": None
        }
        
        # 设置日志
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志系统"""
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 设置日志文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"sequential_automation_{timestamp}.log"
        
        # 初始化日志系统
        logger_setup = ConsoleLoggerSetup()
        logger_setup.setup_logging(
            level=logging.INFO,
            log_file=str(log_file)
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("=" * 60)
        self.logger.info("顺序自动化任务执行器启动")
        self.logger.info("=" * 60)
    
    def check_environment(self) -> bool:
        """检查运行环境"""
        print("检查运行环境...")
        
        # 检查 API 服务
        if not self.browser_api.check_api_server():
            print("❌ ixBrowser API 服务不可用")
            print("请确保 ixBrowser 软件正在运行")
            return False
        
        print("✅ ixBrowser API 服务正常")
        
        # 检查配置文件
        try:
            config = self.config_manager.load_config()
            browsers = config.get("browsers", [])
            enabled_browsers = [b for b in browsers if b.get("enabled", True)]
            
            if not enabled_browsers:
                print("❌ 没有启用的浏览器配置")
                return False
            
            print(f"✅ 找到 {len(enabled_browsers)} 个启用的浏览器配置")
            return True
            
        except Exception as e:
            print(f"❌ 配置文件错误: {e}")
            return False
    
    def get_random_scroll_count(self, base_count: int = 50) -> int:
        """
        获取随机的滚动次数 (50-100 之间)
        
        Args:
            base_count: 基础次数
            
        Returns:
            int: 随机滚动次数
        """
        min_count = max(50, base_count)  # 确保最小是 50
        max_count = max(100, base_count * 2)  # 确保最大是 100 或 base_count*2
        return random.randint(min_count, min_count + 50)  # 50-100 之间
    
    def display_execution_plan(self, browsers: List[Dict[str, Any]], settings: Dict[str, Any]):
        """显示执行计划"""
        print("\n" + "=" * 60)
        print("📋 执行计划")
        print("=" * 60)
        
        print(f"执行模式: 顺序执行 (一个接一个)")
        print(f"浏览器数量: {len(browsers)} 个")
        print(f"观看时间: {settings.get('min_watch_time', 3)}-{settings.get('max_watch_time', 8)} 秒")
        print(f"点赞概率: {settings.get('like_probability', 0.3)} ({settings.get('like_probability', 0.3)*100:.0f}%)")
        print(f"订阅概率: {settings.get('subscribe_probability', 0.1)} ({settings.get('subscribe_probability', 0.1)*100:.0f}%)")
        print(f"滚动次数: 50-100 次 (随机)")
        
        print(f"\n📝 浏览器执行顺序:")
        for i, browser in enumerate(browsers, 1):
            scroll_count = self.get_random_scroll_count(settings.get('scroll_count', 50))
            print(f"  {i}. {browser.get('name', '未知')} (ID: {browser.get('id', '未知')}) - {scroll_count} 个视频")
        
        # 估算总时间
        avg_time_per_video = (settings.get('min_watch_time', 3) + settings.get('max_watch_time', 8)) / 2 + 2  # 加上操作时间
        total_videos = len(browsers) * 75  # 平均 75 个视频
        estimated_minutes = (total_videos * avg_time_per_video) / 60
        
        print(f"\n⏱️ 预估总时间: {estimated_minutes:.0f} 分钟")
        print("=" * 60)
    
    async def run_single_browser(self, browser_data: Dict[str, Any], settings: Dict[str, Any]) -> Dict[str, int]:
        """
        运行单个浏览器的自动化任务
        
        Args:
            browser_data: 浏览器配置数据
            settings: 运行设置
            
        Returns:
            Dict[str, int]: 运行统计数据
        """
        browser_id = browser_data["id"]
        browser_name = browser_data["name"]
        
        # 生成随机滚动次数
        original_scroll_count = settings.get("scroll_count", 50)
        random_scroll_count = self.get_random_scroll_count(original_scroll_count)
        
        # 更新设置中的滚动次数
        current_settings = settings.copy()
        current_settings["scroll_count"] = random_scroll_count
        
        print(f"\n🚀 开始执行: {browser_name} (ID: {browser_id})")
        print(f"📊 目标视频数: {random_scroll_count} 个")
        self.logger.info(f"开始执行浏览器: {browser_name} (ID: {browser_id}), 目标视频: {random_scroll_count}")
        
        bot = None
        try:
            # 1. 打开浏览器
            print(f"🔗 正在打开浏览器...")
            ws_endpoint = self.browser_api.open_browser(browser_id)
            
            if not ws_endpoint:
                raise Exception("无法获取 WebSocket 端点")
            
            print(f"✅ 浏览器打开成功")
            
            # 2. 创建机器人实例
            bot = YouTubeBot(browser_id, ws_endpoint)
            
            # 3. 初始化浏览器连接
            print(f"🔧 正在初始化浏览器连接...")
            if not await bot.init_browser():
                raise Exception("浏览器初始化失败")
            
            print(f"✅ 浏览器初始化成功")
            
            # 4. 运行自动化任务
            print(f"🎬 开始自动化任务...")
            start_time = time.time()
            
            final_stats = await bot.run(current_settings)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 5. 显示结果
            print(f"✅ {browser_name} 任务完成!")
            print(f"📊 统计结果:")
            print(f"   - 观看视频: {final_stats.get('videos_watched', 0)} 个")
            print(f"   - 点赞次数: {final_stats.get('likes_given', 0)} 次")
            print(f"   - 订阅次数: {final_stats.get('subscriptions_made', 0)} 次")
            print(f"   - 错误次数: {final_stats.get('errors', 0)} 次")
            print(f"   - 执行时间: {duration/60:.1f} 分钟")
            
            self.logger.info(f"浏览器 {browser_name} 任务完成 - "
                           f"观看: {final_stats.get('videos_watched', 0)}, "
                           f"点赞: {final_stats.get('likes_given', 0)}, "
                           f"订阅: {final_stats.get('subscriptions_made', 0)}, "
                           f"时间: {duration/60:.1f}分钟")
            
            return final_stats
            
        except Exception as e:
            error_msg = f"浏览器 {browser_name} 执行失败: {e}"
            print(f"❌ {error_msg}")
            self.logger.error(error_msg)
            return {"videos_watched": 0, "likes_given": 0, "subscriptions_made": 0, "errors": 1}
        
        finally:
            # 6. 清理资源
            if bot:
                await bot.cleanup()
            
            # 7. 关闭浏览器
            try:
                self.browser_api.close_browser(browser_id)
                print(f"🔒 浏览器 {browser_name} 已关闭")
            except Exception as e:
                self.logger.warning(f"关闭浏览器 {browser_name} 时出错: {e}")
    
    async def run_sequential_automation(self):
        """运行顺序自动化任务"""
        try:
            # 1. 加载配置
            config = self.config_manager.load_config()
            browsers = config.get("browsers", [])
            settings = config.get("settings", {})
            
            # 2. 筛选启用的浏览器
            enabled_browsers = [b for b in browsers if b.get("enabled", True)]
            
            if not enabled_browsers:
                print("❌ 没有启用的浏览器配置")
                return
            
            # 3. 显示执行计划
            self.display_execution_plan(enabled_browsers, settings)
            
            # 4. 确认执行
            confirm = input(f"\n确认开始顺序执行 {len(enabled_browsers)} 个浏览器的自动化任务? (y/N): ").strip().lower()
            if confirm != 'y':
                print("❌ 任务已取消")
                return
            
            # 5. 记录开始时间
            self.total_stats["start_time"] = time.time()
            
            print(f"\n🎯 开始顺序执行自动化任务...")
            print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 6. 逐个执行浏览器任务
            for i, browser_data in enumerate(enabled_browsers, 1):
                print(f"\n{'='*60}")
                print(f"📍 进度: {i}/{len(enabled_browsers)}")
                print(f"{'='*60}")
                
                # 执行单个浏览器任务
                stats = await self.run_single_browser(browser_data, settings)
                
                # 更新总统计
                self.total_stats["browsers_completed"] += 1
                self.total_stats["total_videos_watched"] += stats.get("videos_watched", 0)
                self.total_stats["total_likes_given"] += stats.get("likes_given", 0)
                self.total_stats["total_subscriptions_made"] += stats.get("subscriptions_made", 0)
                self.total_stats["total_errors"] += stats.get("errors", 0)
                
                # 如果不是最后一个浏览器，等待一段时间
                if i < len(enabled_browsers):
                    wait_time = random.randint(5, 15)  # 5-15秒随机等待
                    print(f"⏳ 等待 {wait_time} 秒后继续下一个浏览器...")
                    await asyncio.sleep(wait_time)
            
            # 7. 记录结束时间
            self.total_stats["end_time"] = time.time()
            
            # 8. 显示最终统计
            self.display_final_summary()
            
        except KeyboardInterrupt:
            print(f"\n\n⚠️ 用户中断任务")
            self.logger.info("用户中断了顺序自动化任务")
        except Exception as e:
            print(f"\n❌ 执行过程中发生错误: {e}")
            self.logger.error(f"顺序自动化任务执行错误: {e}")
    
    def display_final_summary(self):
        """显示最终统计摘要"""
        duration = self.total_stats["end_time"] - self.total_stats["start_time"]
        
        print(f"\n" + "=" * 60)
        print(f"🎉 所有任务执行完成!")
        print(f"=" * 60)
        print(f"📊 总体统计:")
        print(f"   - 完成浏览器: {self.total_stats['browsers_completed']} 个")
        print(f"   - 总观看视频: {self.total_stats['total_videos_watched']} 个")
        print(f"   - 总点赞次数: {self.total_stats['total_likes_given']} 次")
        print(f"   - 总订阅次数: {self.total_stats['total_subscriptions_made']} 次")
        print(f"   - 总错误次数: {self.total_stats['total_errors']} 次")
        print(f"   - 总执行时间: {duration/60:.1f} 分钟")
        print(f"   - 平均每浏览器: {duration/60/max(1, self.total_stats['browsers_completed']):.1f} 分钟")
        
        if self.total_stats['total_videos_watched'] > 0:
            print(f"   - 平均每视频: {duration/max(1, self.total_stats['total_videos_watched']):.1f} 秒")
        
        print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"=" * 60)
        
        self.logger.info(f"顺序自动化任务全部完成 - "
                        f"浏览器: {self.total_stats['browsers_completed']}, "
                        f"视频: {self.total_stats['total_videos_watched']}, "
                        f"点赞: {self.total_stats['total_likes_given']}, "
                        f"订阅: {self.total_stats['total_subscriptions_made']}, "
                        f"时间: {duration/60:.1f}分钟")


async def main():
    """主函数"""
    automation = SequentialAutomation()
    
    # 检查环境
    if not automation.check_environment():
        return
    
    # 运行顺序自动化
    await automation.run_sequential_automation()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序启动失败: {e}")
        sys.exit(1)
    
    print("\n按 Enter 键退出...")
    input()
