"""
ixBrowser API 封装类
用于与 ixBrowser 本地 API 进行交互
"""

import requests
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class ixBrowserAPI:
    """ixBrowser API 封装类"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:53200", timeout: int = 30):
        """
        初始化 API 客户端
        
        Args:
            base_url: API 基础 URL
            timeout: 请求超时时间（秒）
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        
    def check_api_server(self) -> bool:
        """
        检查 ixBrowser API 服务是否正在运行
        
        Returns:
            bool: 服务是否可用
        """
        try:
            response = self.session.get(
                f"{self.base_url}/api/v2/browser/list",
                timeout=self.timeout
            )
            return response.status_code == 200
        except requests.exceptions.RequestException as e:
            logger.error(f"API 服务检查失败: {e}")
            return False
    
    def open_browser(self, browser_id: str) -> Optional[str]:
        """
        打开指定的浏览器实例
        
        Args:
            browser_id: 浏览器 ID
            
        Returns:
            Optional[str]: WebSocket 端点 URL，失败时返回 None
        """
        try:
            url = f"{self.base_url}/api/v2/browser/open"
            params = {"id": browser_id}
            
            logger.info(f"正在打开浏览器: {browser_id}")
            response = self.session.get(url, params=params, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    ws_endpoint = data.get("data", {}).get("ws")
                    if ws_endpoint:
                        logger.info(f"浏览器 {browser_id} 打开成功，WebSocket: {ws_endpoint}")
                        return ws_endpoint
                    else:
                        logger.error(f"响应中缺少 WebSocket 端点: {data}")
                else:
                    logger.error(f"打开浏览器失败: {data.get('message', '未知错误')}")
            else:
                logger.error(f"HTTP 请求失败: {response.status_code} - {response.text}")
                
        except requests.exceptions.RequestException as e:
            logger.error(f"打开浏览器 {browser_id} 时发生网络错误: {e}")
        except Exception as e:
            logger.error(f"打开浏览器 {browser_id} 时发生未知错误: {e}")
            
        return None
    
    def close_browser(self, browser_id: str) -> bool:
        """
        关闭指定的浏览器实例
        
        Args:
            browser_id: 浏览器 ID
            
        Returns:
            bool: 是否成功关闭
        """
        try:
            url = f"{self.base_url}/api/v2/browser/close"
            params = {"id": browser_id}
            
            logger.info(f"正在关闭浏览器: {browser_id}")
            response = self.session.get(url, params=params, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    logger.info(f"浏览器 {browser_id} 关闭成功")
                    return True
                else:
                    logger.error(f"关闭浏览器失败: {data.get('message', '未知错误')}")
            else:
                logger.error(f"HTTP 请求失败: {response.status_code} - {response.text}")
                
        except requests.exceptions.RequestException as e:
            logger.error(f"关闭浏览器 {browser_id} 时发生网络错误: {e}")
        except Exception as e:
            logger.error(f"关闭浏览器 {browser_id} 时发生未知错误: {e}")
            
        return False
    
    def get_browser_list(self) -> Optional[Dict[str, Any]]:
        """
        获取浏览器列表
        
        Returns:
            Optional[Dict]: 浏览器列表数据，失败时返回 None
        """
        try:
            url = f"{self.base_url}/api/v2/browser/list"
            response = self.session.get(url, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 0:
                    return data.get("data", {})
                else:
                    logger.error(f"获取浏览器列表失败: {data.get('message', '未知错误')}")
            else:
                logger.error(f"HTTP 请求失败: {response.status_code} - {response.text}")
                
        except requests.exceptions.RequestException as e:
            logger.error(f"获取浏览器列表时发生网络错误: {e}")
        except Exception as e:
            logger.error(f"获取浏览器列表时发生未知错误: {e}")
            
        return None
