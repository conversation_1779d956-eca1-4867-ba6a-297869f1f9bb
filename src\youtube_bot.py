"""
YouTube Shorts 机器人核心逻辑
使用 Playwright 进行浏览器自动化
"""

import asyncio
import random
import logging
import time
from typing import Dict, Any, List, Optional
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, Playwright, BrowserContext, TimeoutError
from utils.config_manager import ConfigManager

logger = logging.getLogger(__name__)


class YouTubeBot:
    """YouTube Shorts 自动化机器人"""

    def __init__(self, browser_id: str, ws_endpoint: str):
        """
        初始化机器人

        Args:
            browser_id: 浏览器 ID
            ws_endpoint: WebSocket 端点
        """
        self.browser_id = browser_id
        self.ws_endpoint = ws_endpoint
        self.running = False

        # Playwright 对象
        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None

        # 配置管理器
        self.config_manager = ConfigManager()
        self.xpath_config = self.config_manager.load_xpath_config()

        # 统计数据
        self.stats = {
            "videos_watched": 0,
            "likes_given": 0,
            "subscriptions_made": 0,
            "errors": 0
        }
    
    async def random_delay(self, min_seconds: float, max_seconds: float):
        """随机延迟等待"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)

    async def init_browser(self) -> bool:
        """
        初始化浏览器连接

        Returns:
            bool: 是否初始化成功
        """
        try:
            logger.info(f"正在初始化浏览器连接: {self.browser_id}")

            # 启动 Playwright
            self.playwright = await async_playwright().start()

            # 连接到现有浏览器实例，增加超时时间
            self.browser = await self.playwright.chromium.connect_over_cdp(
                self.ws_endpoint,
                timeout=30000  # 增加超时时间为30秒
            )

            # 获取已有的context
            contexts = self.browser.contexts
            if contexts:
                self.context = contexts[0]
            else:
                # 如果没有现有的context，创建一个新的
                self.context = await self.browser.new_context(
                    viewport={"width": 480, "height": 720},  # 设置适合Shorts的视口大小
                    user_agent="Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"
                )

            # 获取已有的页面，如果没有则创建新页面
            pages = self.context.pages
            if pages:
                self.page = pages[0]  # 使用第一个已存在的页面
                # 关闭其他页面
                for page in pages[1:]:
                    await page.close()
                logger.info(f"使用现有页面: {await self.page.title()}")
            else:
                self.page = await self.context.new_page()
                logger.info("创建新页面")

            # 设置页面超时时间
            self.page.set_default_timeout(20000)  # 设置为20秒

            logger.info(f"浏览器 {self.browser_id} 初始化成功")
            return True

        except Exception as e:
            logger.error(f"初始化浏览器 {self.browser_id} 失败: {e}")
            await self.cleanup()
            return False

    async def check_connection(self):
        """检查浏览器连接是否仍然有效"""
        try:
            # 尝试获取当前URL，如果成功则连接正常
            await self.page.evaluate("() => document.location.href")
            return True
        except Exception:
            return False

    async def safe_click(self, selector, timeout=5000, retry=2):
        """安全点击元素，带重试机制"""
        for attempt in range(retry):
            try:
                # 尝试使用更精确的定位方式
                element = self.page.locator(selector).first

                # 等待元素可见并可点击
                await element.wait_for(state="visible", timeout=timeout)

                # 执行点击
                await element.click(timeout=timeout)
                return True
            except Exception as e:
                if attempt < retry - 1:
                    logger.warning(f"机器人 {self.browser_id} 点击 {selector} 失败，重试中: {e}")
                    await self.random_delay(0.5, 1.0)
                else:
                    logger.error(f"机器人 {self.browser_id} 点击 {selector} 失败: {e}")
                    return False

    async def run(self, settings: Dict[str, Any]) -> Dict[str, int]:
        """
        运行机器人主循环

        Args:
            settings: 运行设置

        Returns:
            Dict[str, int]: 运行统计数据
        """
        self.running = True
        scroll_count = settings.get("scroll_count", 50)

        logger.info(f"开始运行机器人 {self.browser_id}，目标视频数: {scroll_count}")

        try:
            # 确保页面已加载完成
            await self.random_delay(2, 4)

            # 如果当前不是YouTube Shorts页面，则导航过去
            current_url = self.page.url
            if 'youtube.com/shorts' not in current_url:
                try:
                    await self.page.goto('https://www.youtube.com/shorts', timeout=30000)
                    await self.random_delay(3, 5)
                except Exception as e:
                    logger.warning(f"机器人 {self.browser_id} 导航到YouTube Shorts失败，尝试重新加载: {e}")
                    await self.page.reload()
                    await self.random_delay(3, 5)

            # 设置连接监控
            last_connection_check = time.time()
            connection_check_interval = 30  # 每30秒检查一次

            for i in range(scroll_count):
                if not self.running:
                    logger.info(f"机器人 {self.browser_id} 收到停止信号")
                    break

                # 定期检查连接是否有效
                if time.time() - last_connection_check > connection_check_interval:
                    if not await self.check_connection():
                        logger.error(f"机器人 {self.browser_id} 浏览器连接已断开，停止任务")
                        break
                    last_connection_check = time.time()

                logger.info(f"机器人 {self.browser_id} 处理第 {i+1}/{scroll_count} 个视频")

                # 观看视频
                watch_time = random.randint(
                    settings.get("min_watch_time", 3),
                    settings.get("max_watch_time", 8)
                )
                if not await self.watch_video(watch_time):
                    # 视频观看失败，可能是连接问题
                    if not await self.check_connection():
                        break

                # 检查是否应该停止
                if not self.running:
                    break

                # 尝试点赞
                await self.try_like(settings.get("like_probability", 0.3))

                # 尝试订阅
                await self.try_subscribe(settings.get("subscribe_probability", 0.1))

                # 检查是否应该停止
                if not self.running:
                    break

                # 滚动到下一个视频
                if i < scroll_count - 1:  # 最后一个视频不需要滚动
                    if not await self.scroll_to_next():
                        # 滚动失败，可能是连接问题
                        if not await self.check_connection():
                            break

            logger.info(f"机器人 {self.browser_id} 任务完成 - "
                       f"观看: {self.stats['videos_watched']}, "
                       f"点赞: {self.stats['likes_given']}, "
                       f"订阅: {self.stats['subscriptions_made']}")

        except Exception as e:
            logger.error(f"机器人 {self.browser_id} 运行时发生错误: {e}")
            self.stats["errors"] += 1

        finally:
            self.running = False
            logger.info(f"机器人 {self.browser_id} 运行完成")

        return self.stats.copy()
    
    async def watch_video(self, watch_time: int) -> bool:
        """
        观看当前视频

        Args:
            watch_time: 观看时间(秒)

        Returns:
            bool: 是否观看成功
        """
        try:
            # 模拟观看行为，使用随机延迟
            await self.random_delay(watch_time * 0.8, watch_time * 1.2)
            self.stats["videos_watched"] += 1
            logger.info(f"机器人 {self.browser_id} 完成视频观看 #{self.stats['videos_watched']}")
            return True
        except Exception as e:
            logger.error(f"机器人 {self.browser_id} 观看视频时出错: {e}")
            self.stats["errors"] += 1
            return False
    
    async def try_like(self, probability: float):
        """
        尝试点赞当前视频

        Args:
            probability: 点赞概率 (0.0 - 1.0)
        """
        try:
            if not self.running:
                return False

            if random.random() < probability:
                # 尝试查找点赞按钮
                selectors = self.xpath_config.get("selectors", {}).get("like_button", [])

                for selector in selectors:
                    try:
                        like_button = self.page.locator(selector)
                        count = await like_button.count()

                        if count == 1:
                            # 只有一个匹配元素时才点击
                            await like_button.click(timeout=5000)
                            self.stats['likes_given'] += 1
                            logger.info(f"机器人 {self.browser_id} 完成点赞 #{self.stats['likes_given']}")
                            await self.random_delay(0.5, 1.5)
                            return True
                        elif count > 1:
                            # 如果有多个匹配元素，记录日志但不抛出异常
                            logger.warning(f"机器人 {self.browser_id} 发现多个点赞按钮 ({count}个)，跳过点赞")
                            break
                    except Exception:
                        continue

                logger.debug(f"机器人 {self.browser_id} 未找到点赞按钮")
            return False
        except Exception as e:
            logger.error(f"机器人 {self.browser_id} 点赞时出错: {e}")
            self.stats["errors"] += 1
            return False
    
    async def try_subscribe(self, probability: float):
        """
        尝试订阅当前频道

        Args:
            probability: 订阅概率 (0.0 - 1.0)
        """
        try:
            if not self.running:
                return False

            if random.random() < probability:
                # 使用更精确的选择器
                selectors = self.xpath_config.get("selectors", {}).get("subscribe_button", [])

                for selector in selectors:
                    try:
                        sub_button = self.page.locator(selector)
                        count = await sub_button.count()

                        if count == 1:
                            # 只有一个匹配元素时才点击
                            await sub_button.click(timeout=5000)
                            self.stats['subscriptions_made'] += 1
                            logger.info(f"机器人 {self.browser_id} 完成订阅 #{self.stats['subscriptions_made']}")
                            await self.random_delay(0.5, 1.5)
                            return True
                        elif count > 1:
                            logger.warning(f"机器人 {self.browser_id} 发现多个订阅按钮 ({count}个)，跳过订阅")
                            break
                    except Exception:
                        continue

                logger.debug(f"机器人 {self.browser_id} 未找到订阅按钮")
            return False
        except Exception as e:
            logger.error(f"机器人 {self.browser_id} 订阅时出错: {e}")
            self.stats["errors"] += 1
            return False
    
    async def scroll_to_next(self):
        """滚动到下一个视频"""
        try:
            if not self.running:
                return False

            # 使用键盘向下箭头键滚动到下一个短视频
            await self.page.keyboard.press('ArrowDown')
            await self.random_delay(1, 2)
            return True
        except Exception as e:
            logger.error(f"机器人 {self.browser_id} 滚动时出错: {e}")
            self.stats["errors"] += 1
            return False
    
    def stop(self):
        """停止机器人运行"""
        self.running = False
        logger.info(f"机器人 {self.browser_id} 收到停止指令")
    
    async def cleanup(self):
        """清理资源"""
        try:
            # 不关闭浏览器，只关闭多余的页面
            if self.page and self.context and len(self.context.pages) > 1:
                try:
                    await self.page.close()
                except Exception:
                    pass
            logger.info(f"机器人 {self.browser_id} 资源清理完成")
        except Exception as e:
            logger.error(f"机器人 {self.browser_id} 清理资源时发生错误: {e}")
    
    def get_stats(self) -> Dict[str, int]:
        """
        获取统计数据
        
        Returns:
            Dict[str, int]: 统计数据
        """
        return self.stats.copy()
