"""
YouTube Shorts 机器人核心逻辑
使用 Playwright 进行浏览器自动化
"""

import asyncio
import random
import logging
from typing import Dict, Any, List, Optional
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, Page, Playwright
from utils.config_manager import ConfigManager

logger = logging.getLogger(__name__)


class YouTubeBot:
    """YouTube Shorts 自动化机器人"""
    
    def __init__(self, browser_id: str, ws_endpoint: str):
        """
        初始化机器人
        
        Args:
            browser_id: 浏览器 ID
            ws_endpoint: WebSocket 端点
        """
        self.browser_id = browser_id
        self.ws_endpoint = ws_endpoint
        self.running = False
        
        # Playwright 对象
        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        
        # 配置管理器
        self.config_manager = ConfigManager()
        self.xpath_config = self.config_manager.load_xpath_config()
        
        # 统计数据
        self.stats = {
            "videos_watched": 0,
            "likes_given": 0,
            "subscriptions_made": 0,
            "errors": 0
        }
    
    async def init_browser(self) -> bool:
        """
        初始化浏览器连接
        
        Returns:
            bool: 是否初始化成功
        """
        try:
            logger.info(f"正在初始化浏览器连接: {self.browser_id}")
            
            # 启动 Playwright
            self.playwright = await async_playwright().start()
            
            # 连接到现有浏览器实例
            self.browser = await self.playwright.chromium.connect_over_cdp(
                self.ws_endpoint
            )
            
            # 获取现有页面或创建新页面
            pages = self.browser.contexts[0].pages if self.browser.contexts else []
            if pages:
                self.page = pages[0]
                logger.info(f"使用现有页面: {await self.page.title()}")
            else:
                self.page = await self.browser.contexts[0].new_page()
                logger.info("创建新页面")
            
            # 导航到 YouTube Shorts
            await self.page.goto("https://www.youtube.com/shorts", wait_until="networkidle")
            logger.info(f"浏览器 {self.browser_id} 初始化成功")
            
            return True
            
        except Exception as e:
            logger.error(f"初始化浏览器 {self.browser_id} 失败: {e}")
            await self.cleanup()
            return False
    
    async def run(self, settings: Dict[str, Any]) -> Dict[str, int]:
        """
        运行机器人主循环
        
        Args:
            settings: 运行设置
            
        Returns:
            Dict[str, int]: 运行统计数据
        """
        self.running = True
        scroll_count = settings.get("scroll_count", 50)
        
        logger.info(f"开始运行机器人 {self.browser_id}，目标视频数: {scroll_count}")
        
        try:
            for i in range(scroll_count):
                if not self.running:
                    logger.info(f"机器人 {self.browser_id} 收到停止信号")
                    break
                
                logger.info(f"机器人 {self.browser_id} 处理第 {i+1}/{scroll_count} 个视频")
                
                # 观看视频
                await self.watch_video(settings)
                
                # 尝试点赞
                await self.try_like(settings.get("like_probability", 0.3))
                
                # 尝试订阅
                await self.try_subscribe(settings.get("subscribe_probability", 0.1))
                
                # 滚动到下一个视频
                if i < scroll_count - 1:  # 最后一个视频不需要滚动
                    await self.scroll_to_next()
                
                self.stats["videos_watched"] += 1
                
        except Exception as e:
            logger.error(f"机器人 {self.browser_id} 运行时发生错误: {e}")
            self.stats["errors"] += 1
        
        finally:
            self.running = False
            logger.info(f"机器人 {self.browser_id} 运行完成")
        
        return self.stats.copy()
    
    async def watch_video(self, settings: Dict[str, Any]):
        """
        观看当前视频
        
        Args:
            settings: 设置参数
        """
        try:
            min_time = settings.get("min_watch_time", 3)
            max_time = settings.get("max_watch_time", 8)
            watch_time = random.uniform(min_time, max_time)
            
            logger.debug(f"机器人 {self.browser_id} 观看视频 {watch_time:.1f} 秒")
            await asyncio.sleep(watch_time)
            
        except Exception as e:
            logger.error(f"观看视频时发生错误: {e}")
            self.stats["errors"] += 1
    
    async def try_like(self, probability: float):
        """
        尝试点赞当前视频
        
        Args:
            probability: 点赞概率 (0.0 - 1.0)
        """
        try:
            if random.random() > probability:
                return
            
            selectors = self.xpath_config.get("selectors", {}).get("like_button", [])
            
            for selector in selectors:
                try:
                    # 等待元素出现
                    await self.page.wait_for_selector(
                        selector, 
                        timeout=3000,
                        state="visible"
                    )
                    
                    # 点击点赞按钮
                    await self.page.click(selector)
                    logger.info(f"机器人 {self.browser_id} 成功点赞")
                    self.stats["likes_given"] += 1
                    
                    # 添加延迟
                    await asyncio.sleep(
                        self.xpath_config.get("wait_times", {}).get("action_delay", 1)
                    )
                    return
                    
                except Exception:
                    continue
            
            logger.debug(f"机器人 {self.browser_id} 未找到点赞按钮")
            
        except Exception as e:
            logger.error(f"点赞时发生错误: {e}")
            self.stats["errors"] += 1
    
    async def try_subscribe(self, probability: float):
        """
        尝试订阅当前频道
        
        Args:
            probability: 订阅概率 (0.0 - 1.0)
        """
        try:
            if random.random() > probability:
                return
            
            selectors = self.xpath_config.get("selectors", {}).get("subscribe_button", [])
            
            for selector in selectors:
                try:
                    # 等待元素出现
                    await self.page.wait_for_selector(
                        selector, 
                        timeout=3000,
                        state="visible"
                    )
                    
                    # 检查是否已经订阅
                    button = await self.page.query_selector(selector)
                    if button:
                        text = await button.inner_text()
                        if "已订阅" in text or "Subscribed" in text:
                            logger.debug(f"机器人 {self.browser_id} 已经订阅了此频道")
                            return
                    
                    # 点击订阅按钮
                    await self.page.click(selector)
                    logger.info(f"机器人 {self.browser_id} 成功订阅")
                    self.stats["subscriptions_made"] += 1
                    
                    # 添加延迟
                    await asyncio.sleep(
                        self.xpath_config.get("wait_times", {}).get("action_delay", 1)
                    )
                    return
                    
                except Exception:
                    continue
            
            logger.debug(f"机器人 {self.browser_id} 未找到订阅按钮")
            
        except Exception as e:
            logger.error(f"订阅时发生错误: {e}")
            self.stats["errors"] += 1
    
    async def scroll_to_next(self):
        """滚动到下一个视频"""
        try:
            # 使用键盘向下箭头滚动
            await self.page.keyboard.press("ArrowDown")
            
            # 等待页面加载
            await asyncio.sleep(
                self.xpath_config.get("wait_times", {}).get("scroll_delay", 2)
            )
            
            logger.debug(f"机器人 {self.browser_id} 滚动到下一个视频")
            
        except Exception as e:
            logger.error(f"滚动时发生错误: {e}")
            self.stats["errors"] += 1
    
    def stop(self):
        """停止机器人运行"""
        self.running = False
        logger.info(f"机器人 {self.browser_id} 收到停止指令")
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            logger.info(f"机器人 {self.browser_id} 资源清理完成")
        except Exception as e:
            logger.error(f"清理资源时发生错误: {e}")
    
    def get_stats(self) -> Dict[str, int]:
        """
        获取统计数据
        
        Returns:
            Dict[str, int]: 统计数据
        """
        return self.stats.copy()
