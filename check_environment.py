#!/usr/bin/env python3
"""
环境检查脚本
检查运行 YouTube Shorts 机器人所需的环境
"""

import sys
import os
from pathlib import Path

def check_python_version():
    """检查 Python 版本"""
    print("检查 Python 版本...")
    version = sys.version_info
    print(f"当前 Python 版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        print("✓ Python 版本符合要求 (3.8+)")
        return True
    else:
        print("✗ Python 版本过低，需要 3.8 或更高版本")
        return False

def check_dependencies():
    """检查依赖项"""
    print("\n检查依赖项...")
    
    dependencies = [
        ("requests", "HTTP 客户端库"),
        ("playwright", "浏览器自动化库"),
        ("PyQt6", "GUI 框架"),
    ]
    
    missing = []
    available = []
    
    for dep, desc in dependencies:
        try:
            __import__(dep)
            print(f"✓ {desc} ({dep}) - 已安装")
            available.append(dep)
        except ImportError as e:
            print(f"✗ {desc} ({dep}) - 未安装: {e}")
            missing.append(dep)
    
    return missing, available

def check_pyqt6_detailed():
    """详细检查 PyQt6"""
    print("\n详细检查 PyQt6...")

    try:
        # 先尝试导入 QtCore
        from PyQt6.QtCore import PYQT_VERSION_STR
        print(f"✓ PyQt6 QtCore 导入成功，版本: {PYQT_VERSION_STR}")

        try:
            from PyQt6.QtWidgets import QApplication
            print("✓ QtWidgets 模块导入成功")

            # 尝试创建应用程序实例
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            print("✓ QApplication 创建成功")

            return True

        except Exception as e:
            print(f"✗ QtWidgets 导入失败: {e}")
            return False

    except ImportError as e:
        print(f"✗ PyQt6 QtCore 导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ PyQt6 检查失败: {e}")
        return False

def check_playwright():
    """检查 Playwright"""
    print("\n检查 Playwright...")
    
    try:
        import playwright
        print(f"✓ Playwright 已安装")
        
        try:
            from playwright.sync_api import sync_playwright
            print("✓ Playwright sync API 可用")
            
            # 检查浏览器
            try:
                with sync_playwright() as p:
                    browser = p.chromium.launch(headless=True)
                    browser.close()
                print("✓ Chromium 浏览器可用")
                return True
            except Exception as e:
                print(f"⚠ Chromium 浏览器不可用: {e}")
                print("  请运行: playwright install chromium")
                return False
                
        except Exception as e:
            print(f"✗ Playwright API 导入失败: {e}")
            return False
            
    except ImportError as e:
        print(f"✗ Playwright 未安装: {e}")
        return False

def check_project_files():
    """检查项目文件"""
    print("\n检查项目文件...")
    
    required_files = [
        "main.py",
        "requirements.txt",
        "config/config.json",
        "config/xpath_config.json",
        "src/main_window.py",
        "src/browser_api.py",
        "src/youtube_bot.py",
    ]
    
    missing = []
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path}")
            missing.append(file_path)
    
    return len(missing) == 0

def provide_solutions(missing_deps, pyqt6_ok, playwright_ok):
    """提供解决方案"""
    print("\n" + "="*50)
    print("解决方案建议:")
    print("="*50)
    
    if missing_deps:
        print(f"\n1. 安装缺失的依赖项:")
        print(f"   pip install {' '.join(missing_deps)}")
    
    if not pyqt6_ok:
        print(f"\n2. 修复 PyQt6 问题:")
        print(f"   pip uninstall PyQt6 PyQt6-Qt6")
        print(f"   pip install PyQt6")
        print(f"   # 或者尝试:")
        print(f"   pip install PyQt6 --force-reinstall")
    
    if not playwright_ok:
        print(f"\n3. 安装 Playwright 浏览器:")
        print(f"   playwright install chromium")
    
    print(f"\n4. 如果问题仍然存在:")
    print(f"   - 尝试在虚拟环境中运行")
    print(f"   - 检查系统是否缺少 Visual C++ 运行库")
    print(f"   - 更新 pip: python -m pip install --upgrade pip")

def main():
    """主函数"""
    print("YouTube Shorts 机器人 - 环境检查")
    print("="*50)
    
    # 检查 Python 版本
    python_ok = check_python_version()
    
    # 检查依赖项
    missing_deps, _ = check_dependencies()
    
    # 详细检查 PyQt6
    pyqt6_ok = check_pyqt6_detailed()
    
    # 检查 Playwright
    playwright_ok = check_playwright()
    
    # 检查项目文件
    files_ok = check_project_files()
    
    # 总结
    print("\n" + "="*50)
    print("环境检查总结:")
    print("="*50)
    
    checks = [
        ("Python 版本", python_ok),
        ("项目文件", files_ok),
        ("PyQt6 GUI框架", pyqt6_ok),
        ("Playwright 自动化", playwright_ok),
    ]
    
    passed = 0
    for name, status in checks:
        status_text = "✓ 正常" if status else "✗ 异常"
        print(f"{name}: {status_text}")
        if status:
            passed += 1
    
    print(f"\n检查结果: {passed}/{len(checks)} 项通过")
    
    if passed == len(checks):
        print("\n🎉 环境检查全部通过！")
        print("可以运行: python main.py")
    else:
        print("\n⚠️ 环境存在问题，请按照以下建议修复:")
        provide_solutions(missing_deps, pyqt6_ok, playwright_ok)
    
    return passed == len(checks)

if __name__ == "__main__":
    try:
        success = main()
        print(f"\n按 Enter 键退出...")
        input()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n检查过程中发生错误: {e}")
        sys.exit(1)
