import asyncio
import random
import logging
import json
import time
from playwright.async_api import async_playwright, Playwright, TimeoutError

class YouTubeBot:
    """YouTube Shorts 自动化操作机器人"""
    
    def __init__(self, browser_id: str, ws_endpoint: str):
        self.browser_id = browser_id
        self.ws_endpoint = ws_endpoint
        self.browser = None
        self.context = None
        self.page = None
        self.running = True
        self.playwright = None
        
        # 加载XPath配置
        with open('config/xpath_config.json', 'r', encoding='utf-8') as f:
            self.xpath_config = json.load(f)['selectors']
            
        # 统计信息
        self.stats = {
            'videos_watched': 0,
            'likes': 0,
            'subscribes': 0
        }
    
    async def random_delay(self, min_seconds: float, max_seconds: float):
        """随机延迟等待"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)
    
    async def init_browser(self):
        """初始化浏览器"""
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.connect_over_cdp(
                self.ws_endpoint,
                timeout=30000  # 增加超时时间为30秒
            )
            
            # 获取已有的context
            contexts = self.browser.contexts
            if contexts:
                self.context = contexts[0]
            else:
                # 如果没有现有的context，创建一个新的
                self.context = await self.browser.new_context(
                    viewport={"width": 480, "height": 720},  # 设置适合Shorts的视口大小
                    user_agent="Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"
                )
            
            # 获取已有的页面，如果没有则创建新页面
            pages = self.context.pages
            if pages:
                self.page = pages[0]  # 使用第一个已存在的页面
                # 关闭其他页面
                for page in pages[1:]:
                    await page.close()
            else:
                self.page = await self.context.new_page()
            
            # 设置页面超时时间
            self.page.set_default_timeout(20000)  # 设置为20秒
            
            logging.info(f"[{self.browser_id}] 浏览器初始化成功")
            return True
        except Exception as e:
            logging.error(f"[{self.browser_id}] 浏览器初始化失败: {str(e)}")
            return False
    
    async def safe_click(self, selector, timeout=5000, retry=2):
        """安全点击元素，带重试机制"""
        for attempt in range(retry):
            try:
                # 尝试使用更精确的定位方式
                element = self.page.locator(selector).first
                
                # 等待元素可见并可点击
                await element.wait_for(state="visible", timeout=timeout)
                
                # 执行点击
                await element.click(timeout=timeout)
                return True
            except Exception as e:
                if attempt < retry - 1:
                    logging.warning(f"[{self.browser_id}] 点击 {selector} 失败，重试中: {str(e)}")
                    await self.random_delay(0.5, 1.0)
                else:
                    logging.error(f"[{self.browser_id}] 点击 {selector} 失败: {str(e)}")
                    return False
    
    async def watch_video(self, watch_time: int):
        """观看视频
        
        Args:
            watch_time: 观看时间(秒)
        """
        try:
            # 模拟观看行为
            await self.random_delay(watch_time * 0.8, watch_time * 1.2)
            self.stats['videos_watched'] += 1
            logging.info(f"[{self.browser_id}] 完成视频观看 #{self.stats['videos_watched']}")
            return True
        except Exception as e:
            logging.error(f"[{self.browser_id}] 观看视频时出错: {str(e)}")
            return False
    
    async def try_like(self, probability: float):
        """尝试点赞
        
        Args:
            probability: 点赞概率(0-1)
        """
        try:
            if not self.running:
                return False
                
            if random.random() < probability:
                # 尝试查找点赞按钮，如果找不到特定的点赞按钮，则不尝试点击
                like_button = self.page.locator(self.xpath_config['like_button'])
                count = await like_button.count()
                
                if count == 1:
                    # 只有一个匹配元素时才点击
                    await like_button.click(timeout=5000)
                    self.stats['likes'] += 1
                    logging.info(f"[{self.browser_id}] 完成点赞 #{self.stats['likes']}")
                    await self.random_delay(0.5, 1.5)
                    return True
                elif count > 1:
                    # 如果有多个匹配元素，记录日志但不抛出异常
                    logging.warning(f"[{self.browser_id}] 发现多个点赞按钮 ({count}个)，跳过点赞")
            return False
        except Exception as e:
            logging.error(f"[{self.browser_id}] 点赞时出错: {str(e)}")
            return False
    
    async def try_subscribe(self, probability: float):
        """尝试订阅
        
        Args:
            probability: 订阅概率(0-1)
        """
        try:
            if not self.running:
                return False
                
            if random.random() < probability:
                # 使用更精确的选择器
                sub_button = self.page.locator(self.xpath_config['subscribe_button'])
                count = await sub_button.count()
                
                if count == 1:
                    # 只有一个匹配元素时才点击
                    await sub_button.click(timeout=5000)
                    self.stats['subscribes'] += 1
                    logging.info(f"[{self.browser_id}] 完成订阅 #{self.stats['subscribes']}")
                    await self.random_delay(0.5, 1.5)
                    return True
                elif count > 1:
                    logging.warning(f"[{self.browser_id}] 发现多个订阅按钮 ({count}个)，跳过订阅")
            return False
        except Exception as e:
            logging.error(f"[{self.browser_id}] 订阅时出错: {str(e)}")
            return False
    
    async def scroll_to_next(self):
        """滚动到下一个视频"""
        try:
            if not self.running:
                return False
                
            # 使用键盘向下箭头键滚动到下一个短视频
            await self.page.keyboard.press('ArrowDown')
            await self.random_delay(1, 2)
            return True
        except Exception as e:
            logging.error(f"[{self.browser_id}] 滚动时出错: {str(e)}")
            return False
    
    async def check_connection(self):
        """检查浏览器连接是否仍然有效"""
        try:
            # 尝试获取当前URL，如果成功则连接正常
            await self.page.evaluate("() => document.location.href")
            return True
        except Exception:
            return False
    
    async def run(self, settings: dict):
        """运行机器人"""
        try:
            # 初始化浏览器
            if not await self.init_browser():
                return
            
            # 确保页面已加载完成
            await self.random_delay(2, 4)
            
            # 如果当前不是YouTube Shorts页面，则导航过去
            current_url = self.page.url
            if 'youtube.com/shorts' not in current_url:
                try:
                    await self.page.goto('https://www.youtube.com/shorts', timeout=30000)
                    await self.random_delay(3, 5)
                except TimeoutError:
                    logging.warning(f"[{self.browser_id}] 导航到YouTube Shorts超时，尝试重新加载")
                    await self.page.reload()
                    await self.random_delay(3, 5)
            
            # 设置内存监控
            last_memory_check = time.time()
            memory_check_interval = 30  # 每30秒检查一次
            
            # 循环观看视频
            for i in range(settings['scroll_count']):
                if not self.running:
                    break
                
                # 定期检查连接是否有效
                if time.time() - last_memory_check > memory_check_interval:
                    if not await self.check_connection():
                        logging.error(f"[{self.browser_id}] 浏览器连接已断开，停止任务")
                        break
                    last_memory_check = time.time()
                    
                logging.info(f"[{self.browser_id}] 正在观看第 {i+1}/{settings['scroll_count']} 个视频")
                
                # 观看视频
                watch_time = random.randint(
                    settings['watch_time_min'],
                    settings['watch_time_max']
                )
                if not await self.watch_video(watch_time):
                    # 视频观看失败，可能是连接问题
                    if not await self.check_connection():
                        break
                
                # 检查是否应该停止
                if not self.running:
                    break
                
                # 互动
                await self.try_like(settings['like_probability'])
                await self.try_subscribe(settings['subscribe_probability'])
                
                # 检查是否应该停止
                if not self.running:
                    break
                
                # 下一个视频
                if not await self.scroll_to_next():
                    # 滚动失败，可能是连接问题
                    if not await self.check_connection():
                        break
                
            logging.info(f"[{self.browser_id}] 任务完成 - "
                        f"观看: {self.stats['videos_watched']}, "
                        f"点赞: {self.stats['likes']}, "
                        f"订阅: {self.stats['subscribes']}")
                        
        except Exception as e:
            logging.error(f"[{self.browser_id}] 运行出错: {str(e)}")
            raise
        finally:
            # 不关闭浏览器，只关闭多余的页面
            if self.page and self.context and len(self.context.pages) > 1:
                try:
                    await self.page.close() 
                except Exception:
                    pass 