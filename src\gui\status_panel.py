"""
状态监控面板
显示机器人运行状态和统计信息
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any
from PyQt6.QtWidgets import (
    QGroupBox, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QLabel, QFrame, QPushButton, QHeaderView
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont

logger = logging.getLogger(__name__)


class StatusPanel(QGroupBox):
    """状态监控面板"""
    
    # 定义信号
    clear_requested = pyqtSignal()  # 请求清除状态
    
    def __init__(self, parent=None):
        """
        初始化面板
        
        Args:
            parent: 父窗口
        """
        super().__init__("运行状态", parent)
        self.start_time = None
        self.bot_data = {}  # 存储机器人数据
        
        self.setup_ui()
        self.setup_timer()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 总体统计信息
        self.create_summary_section(layout)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(line)
        
        # 机器人状态表格
        self.create_status_table(layout)
        
        # 控制按钮
        self.create_control_buttons(layout)
    
    def create_summary_section(self, layout):
        """创建总体统计部分"""
        summary_frame = QFrame()
        summary_layout = QVBoxLayout(summary_frame)
        
        # 运行时间
        time_layout = QHBoxLayout()
        time_layout.addWidget(QLabel("运行时间:"))
        self.runtime_label = QLabel("00:00:00")
        self.runtime_label.setFont(QFont("Consolas", 12, QFont.Weight.Bold))
        self.runtime_label.setStyleSheet("color: blue;")
        time_layout.addWidget(self.runtime_label)
        time_layout.addStretch()
        summary_layout.addLayout(time_layout)
        
        # 统计信息网格
        stats_layout = QHBoxLayout()
        
        # 活跃机器人
        active_layout = QVBoxLayout()
        active_layout.addWidget(QLabel("活跃机器人:"))
        self.active_bots_label = QLabel("0")
        self.active_bots_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        self.active_bots_label.setStyleSheet("color: green;")
        self.active_bots_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        active_layout.addWidget(self.active_bots_label)
        stats_layout.addLayout(active_layout)
        
        # 总观看数
        watched_layout = QVBoxLayout()
        watched_layout.addWidget(QLabel("总观看数:"))
        self.total_watched_label = QLabel("0")
        self.total_watched_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        self.total_watched_label.setStyleSheet("color: blue;")
        self.total_watched_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        watched_layout.addWidget(self.total_watched_label)
        stats_layout.addLayout(watched_layout)
        
        # 总点赞数
        likes_layout = QVBoxLayout()
        likes_layout.addWidget(QLabel("总点赞数:"))
        self.total_likes_label = QLabel("0")
        self.total_likes_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        self.total_likes_label.setStyleSheet("color: red;")
        self.total_likes_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        likes_layout.addWidget(self.total_likes_label)
        stats_layout.addLayout(likes_layout)
        
        # 总订阅数
        subs_layout = QVBoxLayout()
        subs_layout.addWidget(QLabel("总订阅数:"))
        self.total_subs_label = QLabel("0")
        self.total_subs_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        self.total_subs_label.setStyleSheet("color: purple;")
        self.total_subs_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subs_layout.addWidget(self.total_subs_label)
        stats_layout.addLayout(subs_layout)
        
        summary_layout.addLayout(stats_layout)
        layout.addWidget(summary_frame)
    
    def create_status_table(self, layout):
        """创建状态表格"""
        # 表格标题
        table_label = QLabel("机器人详细状态:")
        table_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout.addWidget(table_label)
        
        # 状态表格
        self.status_table = QTableWidget()
        self.status_table.setColumnCount(6)
        self.status_table.setHorizontalHeaderLabels([
            "浏览器名称", "状态", "观看数", "点赞数", "订阅数", "错误数"
        ])
        
        # 设置表格属性
        self.status_table.setAlternatingRowColors(True)
        self.status_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.status_table.setMinimumHeight(200)
        
        # 设置列宽
        header = self.status_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # 浏览器名称
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # 状态
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # 观看数
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 点赞数
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 订阅数
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # 错误数
        
        layout.addWidget(self.status_table)
    
    def create_control_buttons(self, layout):
        """创建控制按钮"""
        button_layout = QHBoxLayout()
        
        # 刷新按钮
        self.refresh_button = QPushButton("刷新状态")
        self.refresh_button.clicked.connect(self.refresh_status)
        button_layout.addWidget(self.refresh_button)
        
        # 清除按钮
        self.clear_button = QPushButton("清除状态")
        self.clear_button.clicked.connect(self.clear_status)
        button_layout.addWidget(self.clear_button)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
    
    def setup_timer(self):
        """设置定时器"""
        # 运行时间更新定时器
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_runtime)
        self.timer.start(1000)  # 每秒更新一次
    
    def start_monitoring(self):
        """开始监控"""
        self.start_time = datetime.now()
        self.clear_status()
        logger.info("开始状态监控")
    
    def stop_monitoring(self):
        """停止监控"""
        self.start_time = None
        logger.info("停止状态监控")
    
    def update_bot_status(self, browser_id: str, status: str, stats: Dict[str, Any]):
        """
        更新机器人状态
        
        Args:
            browser_id: 浏览器 ID
            status: 状态描述
            stats: 统计数据
        """
        # 更新机器人数据
        if browser_id not in self.bot_data:
            self.bot_data[browser_id] = {
                "name": browser_id,
                "status": status,
                "stats": stats
            }
        else:
            self.bot_data[browser_id]["status"] = status
            self.bot_data[browser_id]["stats"] = stats
        
        # 更新表格
        self.update_status_table()
        
        # 更新总体统计
        self.update_summary_stats()
    
    def remove_bot_status(self, browser_id: str):
        """
        移除机器人状态
        
        Args:
            browser_id: 浏览器 ID
        """
        if browser_id in self.bot_data:
            del self.bot_data[browser_id]
            self.update_status_table()
            self.update_summary_stats()
    
    def update_status_table(self):
        """更新状态表格"""
        # 设置行数
        self.status_table.setRowCount(len(self.bot_data))
        
        # 填充数据
        for row, (browser_id, data) in enumerate(self.bot_data.items()):
            stats = data.get("stats", {})
            
            # 浏览器名称
            name_item = QTableWidgetItem(data.get("name", browser_id))
            self.status_table.setItem(row, 0, name_item)
            
            # 状态
            status_item = QTableWidgetItem(data.get("status", "未知"))
            status = data.get("status", "")
            if "运行" in status:
                status_item.setBackground(Qt.GlobalColor.lightGreen)
            elif "完成" in status:
                status_item.setBackground(Qt.GlobalColor.lightBlue)
            elif "错误" in status or "失败" in status:
                status_item.setBackground(Qt.GlobalColor.lightGray)
            self.status_table.setItem(row, 1, status_item)
            
            # 观看数
            watched_item = QTableWidgetItem(str(stats.get("videos_watched", 0)))
            watched_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.status_table.setItem(row, 2, watched_item)
            
            # 点赞数
            likes_item = QTableWidgetItem(str(stats.get("likes_given", 0)))
            likes_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.status_table.setItem(row, 3, likes_item)
            
            # 订阅数
            subs_item = QTableWidgetItem(str(stats.get("subscriptions_made", 0)))
            subs_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.status_table.setItem(row, 4, subs_item)
            
            # 错误数
            errors_item = QTableWidgetItem(str(stats.get("errors", 0)))
            errors_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            if stats.get("errors", 0) > 0:
                errors_item.setBackground(Qt.GlobalColor.yellow)
            self.status_table.setItem(row, 5, errors_item)
    
    def update_summary_stats(self):
        """更新总体统计"""
        # 计算总数
        active_count = 0
        total_watched = 0
        total_likes = 0
        total_subs = 0
        
        for data in self.bot_data.values():
            status = data.get("status", "")
            if "运行" in status:
                active_count += 1
            
            stats = data.get("stats", {})
            total_watched += stats.get("videos_watched", 0)
            total_likes += stats.get("likes_given", 0)
            total_subs += stats.get("subscriptions_made", 0)
        
        # 更新标签
        self.active_bots_label.setText(str(active_count))
        self.total_watched_label.setText(str(total_watched))
        self.total_likes_label.setText(str(total_likes))
        self.total_subs_label.setText(str(total_subs))
    
    def update_runtime(self):
        """更新运行时间"""
        if self.start_time:
            elapsed = datetime.now() - self.start_time
            # 格式化为 HH:MM:SS
            hours, remainder = divmod(elapsed.total_seconds(), 3600)
            minutes, seconds = divmod(remainder, 60)
            time_str = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
            self.runtime_label.setText(time_str)
        else:
            self.runtime_label.setText("00:00:00")
    
    def refresh_status(self):
        """刷新状态显示"""
        self.update_status_table()
        self.update_summary_stats()
        logger.info("状态显示已刷新")
    
    def clear_status(self):
        """清除状态"""
        self.bot_data.clear()
        self.status_table.setRowCount(0)
        self.update_summary_stats()
        self.clear_requested.emit()
        logger.info("状态已清除")
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """
        获取总体统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        active_count = 0
        total_watched = 0
        total_likes = 0
        total_subs = 0
        total_errors = 0
        
        for data in self.bot_data.values():
            status = data.get("status", "")
            if "运行" in status:
                active_count += 1
            
            stats = data.get("stats", {})
            total_watched += stats.get("videos_watched", 0)
            total_likes += stats.get("likes_given", 0)
            total_subs += stats.get("subscriptions_made", 0)
            total_errors += stats.get("errors", 0)
        
        runtime = "00:00:00"
        if self.start_time:
            elapsed = datetime.now() - self.start_time
            hours, remainder = divmod(elapsed.total_seconds(), 3600)
            minutes, seconds = divmod(remainder, 60)
            runtime = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
        
        return {
            "active_bots": active_count,
            "total_bots": len(self.bot_data),
            "total_watched": total_watched,
            "total_likes": total_likes,
            "total_subscriptions": total_subs,
            "total_errors": total_errors,
            "runtime": runtime
        }
