# YouTube Shorts 机器人

一个基于 PyQt6 和 Playwright 的桌面应用程序，用于在 ixBrowser 环境中自动执行 YouTube Shorts 的用户交互。

## 功能特性

- 🚀 **多浏览器并发运行** - 同时控制多个浏览器实例
- ⚙️ **可配置的自动化行为** - 自定义观看时间、点赞概率、订阅概率
- 📊 **实时状态监控** - 查看每个机器人的运行状态和统计信息
- 📝 **详细日志记录** - 完整的操作日志和错误追踪
- 🎯 **智能元素识别** - 支持多种 YouTube 界面元素选择器
- 💾 **配置管理** - 保存和加载浏览器配置及运行设置

## 技术栈

- **GUI框架**: PyQt6
- **浏览器自动化**: Playwright
- **HTTP客户端**: requests
- **配置管理**: JSON
- **日志系统**: Python logging
- **并发处理**: QThread

## 安装要求

### 系统要求
- Python 3.8+
- Windows 10/11 (推荐)
- ixBrowser 软件

### Python 依赖
```bash
pip install -r requirements.txt
```

### Playwright 浏览器
```bash
playwright install chromium
```

## 快速开始

### 1. 安装依赖
```bash
# 克隆或下载项目
cd ixBrowser_YouTube

# 安装 Python 依赖
pip install -r requirements.txt

# 安装 Playwright 浏览器
playwright install chromium
```

### 2. 配置 ixBrowser
1. 启动 ixBrowser 软件
2. 确保 API 服务运行在 `http://127.0.0.1:53200`
3. 创建并配置浏览器配置文件

### 3. 运行应用程序
```bash
python main.py
```

### 4. 基本使用流程
1. **添加浏览器**: 在浏览器管理面板中添加 ixBrowser 配置文件
2. **配置参数**: 在配置面板中设置观看时间、点赞概率等参数
3. **选择浏览器**: 勾选要使用的浏览器配置文件
4. **开始任务**: 点击"开始任务"按钮启动自动化
5. **监控状态**: 在状态面板中查看实时运行情况
6. **查看日志**: 在日志面板中查看详细操作记录

## 配置说明

### 主配置文件 (`config/config.json`)
```json
{
    "browsers": [
        {
            "id": "browser_001",
            "name": "测试浏览器 1",
            "enabled": true
        }
    ],
    "settings": {
        "min_watch_time": 3,
        "max_watch_time": 8,
        "like_probability": 0.3,
        "subscribe_probability": 0.1,
        "scroll_count": 50
    },
    "api": {
        "base_url": "http://127.0.0.1:53200",
        "timeout": 30
    }
}
```

### 元素选择器配置 (`config/xpath_config.json`)
```json
{
    "selectors": {
        "like_button": [
            "button[aria-label*='like']",
            "button[aria-label*='赞']",
            "#like-button"
        ],
        "subscribe_button": [
            "button[aria-label*='Subscribe']",
            "button[aria-label*='订阅']",
            "#subscribe-button"
        ]
    },
    "wait_times": {
        "element_load": 3,
        "action_delay": 1,
        "scroll_delay": 2
    }
}
```

## 项目结构

```
ixBrowser_YouTube/
├── main.py                    # 应用程序入口点
├── requirements.txt           # Python 依赖列表
├── test_basic.py             # 基本功能测试脚本
├── README.md                 # 项目说明文档
├── config/                   # 配置文件目录
│   ├── config.json          # 主配置文件
│   └── xpath_config.json    # 元素选择器配置
├── src/                     # 源代码目录
│   ├── __init__.py
│   ├── browser_api.py       # ixBrowser API 封装
│   ├── youtube_bot.py       # YouTube 自动化逻辑
│   ├── bot_worker.py        # 工作线程管理
│   ├── main_window.py       # 主窗口界面
│   └── gui/                 # GUI 组件
│       ├── __init__.py
│       ├── browser_panel.py # 浏览器管理面板
│       ├── config_panel.py  # 配置面板
│       ├── status_panel.py  # 状态监控面板
│       └── log_panel.py     # 日志面板
└── utils/                   # 工具模块
    ├── __init__.py
    ├── config_manager.py    # 配置管理工具
    └── logger_setup.py      # 日志设置工具
```

## 测试

运行基本功能测试：
```bash
python test_basic.py
```

测试将验证：
- 文件结构完整性
- 模块导入正确性
- 配置管理功能
- API 连接状态
- GUI 组件创建
- 日志系统功能

## 使用注意事项

1. **ixBrowser 要求**: 确保 ixBrowser 软件正在运行且 API 服务可用
2. **浏览器配置**: 在 ixBrowser 中预先创建和配置好浏览器配置文件
3. **网络环境**: 确保网络连接稳定，能够访问 YouTube
4. **合理使用**: 设置合理的概率和时间间隔，避免过于频繁的操作
5. **监控日志**: 定期查看日志以了解运行状态和可能的问题

## 故障排除

### 常见问题

**Q: API 状态显示离线**
A: 检查 ixBrowser 是否正在运行，API 服务是否启动

**Q: 浏览器无法打开**
A: 确认浏览器 ID 正确，且在 ixBrowser 中存在对应配置

**Q: 找不到页面元素**
A: 检查 `xpath_config.json` 中的选择器是否与当前 YouTube 界面匹配

**Q: Playwright 错误**
A: 运行 `playwright install chromium` 安装浏览器

### 日志文件位置
- 应用程序日志: `logs/youtube_bot_YYYYMMDD.log`
- 控制台输出: 实时显示在日志面板中

## 开发说明

### 添加新功能
1. 在相应模块中实现功能
2. 更新配置文件结构（如需要）
3. 添加相应的 GUI 控件
4. 更新测试脚本

### 自定义元素选择器
编辑 `config/xpath_config.json` 文件，添加新的选择器或修改现有选择器。

## 许可证

本项目仅供学习和研究使用。请遵守 YouTube 的服务条款和相关法律法规。

## 免责声明

本软件仅用于教育和研究目的。使用者应当遵守相关平台的服务条款和适用的法律法规。开发者不对使用本软件可能产生的任何后果承担责任。
