#!/usr/bin/env python3
"""
ixBrowser API 测试工具
用于测试新的 API 接口，获取浏览器列表并手动选择打开
"""

import requests
import json
import sys
from typing import Dict, List, Any, Optional


class ixBrowserAPITester:
    """ixBrowser API 测试器"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:53200"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
    
    def check_api_server(self) -> bool:
        """检查 API 服务是否可用"""
        try:
            # 尝试获取浏览器列表来检查服务
            response = self.session.post(
                f"{self.base_url}/api/v2/profile-list",
                json={"page": 1, "limit": 1},
                timeout=10
            )
            return response.status_code == 200
        except Exception as e:
            print(f"API 服务检查失败: {e}")
            return False
    
    def get_browser_list(self, page: int = 1, limit: int = 50) -> Optional[Dict[str, Any]]:
        """
        获取浏览器列表
        
        Args:
            page: 页数
            limit: 每页数量
            
        Returns:
            浏览器列表数据
        """
        try:
            url = f"{self.base_url}/api/v2/profile-list"
            payload = {
                "profile_id": 0,
                "name": "",
                "group_id": 0,
                "tag_id": 0,
                "page": page,
                "limit": limit
            }
            
            print(f"正在获取浏览器列表...")
            print(f"请求 URL: {url}")
            print(f"请求参数: {json.dumps(payload, indent=2, ensure_ascii=False)}")
            
            response = self.session.post(url, json=payload, timeout=30)
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                return data
            else:
                print(f"请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
                
        except Exception as e:
            print(f"获取浏览器列表失败: {e}")
            return None
    
    def open_browser(self, profile_id: int) -> Optional[str]:
        """
        打开浏览器
        
        Args:
            profile_id: 浏览器配置文件 ID
            
        Returns:
            WebSocket 端点 URL
        """
        try:
            url = f"{self.base_url}/api/v2/profile-open"
            payload = {
                "profile_id": profile_id,
                "args": ["--disable-extension-welcome-page"],
                "load_extensions": True,
                "load_profile_info_page": True,
                "cookies_backup": True,
                "cookie": ""
            }
            
            print(f"正在打开浏览器...")
            print(f"请求 URL: {url}")
            print(f"请求参数: {json.dumps(payload, indent=2, ensure_ascii=False)}")
            
            response = self.session.post(url, json=payload, timeout=30)
            
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                # 尝试提取 WebSocket 端点
                if isinstance(data, dict):
                    # 可能的字段名
                    ws_fields = ['ws', 'websocket', 'ws_endpoint', 'cdp_url', 'debugger_url']
                    for field in ws_fields:
                        if field in data:
                            return data[field]
                    
                    # 如果在 data 字段中
                    if 'data' in data and isinstance(data['data'], dict):
                        for field in ws_fields:
                            if field in data['data']:
                                return data['data'][field]
                
                print("警告: 未找到 WebSocket 端点")
                return None
            else:
                print(f"打开浏览器失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
                
        except Exception as e:
            print(f"打开浏览器失败: {e}")
            return None
    
    def close_browser(self, profile_id: int) -> bool:
        """
        关闭浏览器
        
        Args:
            profile_id: 浏览器配置文件 ID
            
        Returns:
            是否成功关闭
        """
        try:
            # 尝试不同的关闭接口
            close_urls = [
                f"{self.base_url}/api/v2/profile-close",
                f"{self.base_url}/api/v2/browser/close"
            ]
            
            for url in close_urls:
                try:
                    # 尝试 POST 方法
                    response = self.session.post(
                        url, 
                        json={"profile_id": profile_id}, 
                        timeout=10
                    )
                    if response.status_code == 200:
                        print(f"浏览器 {profile_id} 关闭成功 (POST {url})")
                        return True
                    
                    # 尝试 GET 方法（兼容旧接口）
                    response = self.session.get(
                        url, 
                        params={"id": profile_id}, 
                        timeout=10
                    )
                    if response.status_code == 200:
                        print(f"浏览器 {profile_id} 关闭成功 (GET {url})")
                        return True
                        
                except Exception:
                    continue
            
            print(f"无法关闭浏览器 {profile_id}")
            return False
            
        except Exception as e:
            print(f"关闭浏览器失败: {e}")
            return False


def main():
    """主函数"""
    print("ixBrowser API 测试工具")
    print("=" * 50)
    
    # 创建 API 测试器
    api = ixBrowserAPITester()
    
    # 检查 API 服务
    print("1. 检查 API 服务...")
    if not api.check_api_server():
        print("❌ ixBrowser API 服务不可用")
        print("请确保 ixBrowser 软件正在运行")
        return
    
    print("✅ ixBrowser API 服务正常")
    
    # 获取浏览器列表
    print("\n2. 获取浏览器列表...")
    browser_data = api.get_browser_list()
    
    if not browser_data:
        print("❌ 无法获取浏览器列表")
        return
    
    # 解析浏览器列表
    browsers = []
    if isinstance(browser_data, dict):
        # 新 API 格式: data.data
        if 'data' in browser_data and isinstance(browser_data['data'], dict):
            if 'data' in browser_data['data'] and isinstance(browser_data['data']['data'], list):
                browsers = browser_data['data']['data']
        # 备用格式
        elif 'data' in browser_data and isinstance(browser_data['data'], list):
            browsers = browser_data['data']
        elif 'list' in browser_data and isinstance(browser_data['list'], list):
            browsers = browser_data['list']
        elif isinstance(browser_data, list):
            browsers = browser_data
    
    if not browsers:
        print("❌ 没有找到浏览器配置")
        return
    
    print(f"✅ 找到 {len(browsers)} 个浏览器配置")
    
    # 显示浏览器列表
    print("\n3. 浏览器列表:")
    print("-" * 50)
    for i, browser in enumerate(browsers, 1):
        profile_id = browser.get('profile_id', browser.get('id', '未知'))
        name = browser.get('name', browser.get('profile_name', '未命名'))
        status = browser.get('status', '未知状态')
        print(f"{i}. ID: {profile_id} | 名称: {name} | 状态: {status}")
    
    # 让用户选择浏览器
    print("\n4. 选择要打开的浏览器:")
    try:
        choice = input(f"请输入序号 (1-{len(browsers)}) 或 0 退出: ").strip()
        
        if choice == "0":
            print("退出程序")
            return
        
        index = int(choice) - 1
        if 0 <= index < len(browsers):
            selected_browser = browsers[index]
            profile_id = selected_browser.get('profile_id', selected_browser.get('id'))
            name = selected_browser.get('name', selected_browser.get('profile_name', '未命名'))
            
            print(f"\n5. 正在打开浏览器: {name} (ID: {profile_id})")
            
            # 打开浏览器
            ws_endpoint = api.open_browser(profile_id)
            
            if ws_endpoint:
                print(f"✅ 浏览器打开成功!")
                print(f"WebSocket 端点: {ws_endpoint}")
                
                # 询问是否关闭
                close_choice = input("\n是否关闭浏览器? (y/N): ").strip().lower()
                if close_choice == 'y':
                    if api.close_browser(profile_id):
                        print("✅ 浏览器已关闭")
                    else:
                        print("⚠️ 浏览器关闭可能失败，请手动检查")
                else:
                    print("浏览器保持打开状态")
            else:
                print("❌ 浏览器打开失败")
        else:
            print("❌ 无效的选择")
            
    except ValueError:
        print("❌ 请输入有效的数字")
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"❌ 发生错误: {e}")


if __name__ == "__main__":
    main()
    input("\n按 Enter 键退出...")
