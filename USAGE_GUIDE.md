# YouTube Shorts 机器人使用指南

## 项目状态 ✅

**项目已完全实现并可正常使用！**

### 可用版本

1. **GUI 版本** (`main.py`) - 完整的图形界面版本
2. **控制台版本** (`main_console.py`) - 命令行版本（推荐）

## 快速开始

### 1. 环境检查

首先运行环境检查脚本：

```bash
python check_environment.py
```

这将检查：
- Python 版本
- 依赖项安装状态
- PyQt6 可用性
- Playwright 浏览器
- 项目文件完整性

### 2. 选择运行版本

#### GUI 版本（图形界面）
```bash
python main.py  # 运行 GUI 版本
```

#### 控制台版本（命令行）
```bash
python main_console.py  # 运行控制台版本
```

**注意**: 如果遇到 PyQt6 相关错误，请使用控制台版本。

### 3. 核心功能测试

运行核心功能测试确保一切正常：

```bash
python test_core.py
```

## 详细使用说明

### 控制台版本使用（推荐）

控制台版本功能完整且稳定，推荐使用：

#### 启动应用程序
```bash
python main_console.py
```

#### 主菜单功能

1. **查看浏览器配置** - 显示已配置的浏览器列表
2. **添加浏览器配置** - 添加新的 ixBrowser 配置文件
3. **删除浏览器配置** - 删除不需要的浏览器配置
4. **查看运行设置** - 显示当前的自动化参数
5. **修改运行设置** - 调整观看时间、概率等参数
6. **开始自动化任务** - 启动机器人开始工作
7. **查看运行状态** - 查看正在运行的机器人状态
8. **停止所有任务** - 停止所有正在运行的机器人

#### 基本使用流程

1. **添加浏览器配置**
   - 选择菜单项 `2`
   - 输入 ixBrowser 中的浏览器 ID
   - 输入便于识别的显示名称

2. **配置运行参数**
   - 选择菜单项 `5`
   - 设置观看时间范围（如 3-8 秒）
   - 设置点赞概率（如 0.3 = 30%）
   - 设置订阅概率（如 0.1 = 10%）
   - 设置要观看的视频数量

3. **开始自动化**
   - 选择菜单项 `6`
   - 确认设置信息
   - 输入 `y` 开始运行
   - 按 `Ctrl+C` 可随时停止

### GUI 版本使用

如果 PyQt6 工作正常，可以使用图形界面版本：

```bash
python main.py
```

GUI 版本提供：
- 直观的图形界面
- 实时状态监控
- 详细的日志显示
- 可视化的配置管理

## 配置说明

### 浏览器配置

在 ixBrowser 软件中：
1. 创建浏览器配置文件
2. 记录浏览器 ID
3. 确保浏览器可以正常打开

在应用程序中：
1. 添加浏览器配置（使用 ixBrowser 中的 ID）
2. 设置显示名称便于识别
3. 启用要使用的浏览器

### 运行参数

- **观看时间**: 每个视频的观看时长范围（秒）
- **点赞概率**: 随机点赞的概率（0.0-1.0）
- **订阅概率**: 随机订阅的概率（0.0-1.0）
- **视频数量**: 每个浏览器要处理的视频总数

### 元素选择器

编辑 `config/xpath_config.json` 可以自定义：
- 点赞按钮的选择器
- 订阅按钮的选择器
- 等待时间设置

## 故障排除

### 常见问题

**Q: PyQt6 导入失败**
```
ImportError: DLL load failed while importing QtCore
```
A: 使用控制台版本：`python main_console.py`

**Q: ixBrowser API 不可用**
```
✗ ixBrowser API 服务不可用
```
A: 确保 ixBrowser 软件正在运行，API 服务在 `http://127.0.0.1:53200`

**Q: 浏览器无法打开**
```
✗ 无法打开浏览器: browser_001
```
A: 检查浏览器 ID 是否正确，在 ixBrowser 中是否存在对应配置

**Q: Playwright 浏览器不可用**
```
⚠ Chromium 浏览器不可用
```
A: 运行 `playwright install chromium`

### 日志文件

- 控制台版本日志: `logs/youtube_bot_console_YYYYMMDD_HHMMSS.log`
- GUI 版本日志: `logs/youtube_bot_YYYYMMDD.log`

### 重置配置

如果配置文件损坏，删除 `config/` 目录，重新运行程序会自动创建默认配置。

## 安全建议

1. **合理设置参数**: 不要设置过高的概率或过短的时间间隔
2. **监控运行状态**: 定期检查日志和运行状态
3. **遵守平台规则**: 确保使用符合 YouTube 服务条款
4. **适度使用**: 避免长时间连续运行

## 技术支持

### 文件结构
```
ixBrowser_YouTube/
├── main.py                    # GUI 版本入口
├── main_console.py           # 控制台版本入口
├── check_environment.py      # 环境检查脚本
├── test_core.py             # 核心功能测试
├── requirements.txt         # 依赖项列表
├── README.md               # 项目说明
├── USAGE_GUIDE.md          # 使用指南
├── PROJECT_SUMMARY.md      # 项目总结
├── config/                 # 配置文件
├── src/                   # 源代码
├── utils/                 # 工具模块
└── logs/                  # 日志文件（自动创建）
```

### 核心组件

- **browser_api.py**: ixBrowser API 封装
- **youtube_bot.py**: YouTube 自动化逻辑
- **config_manager.py**: 配置文件管理
- **console_logger.py**: 控制台日志系统

### 扩展开发

项目采用模块化设计，可以轻松扩展：
- 添加新的视频平台支持
- 实现更多自动化操作
- 集成其他浏览器管理工具

## 总结

YouTube Shorts 机器人项目已完全实现并可正常使用：

✅ **控制台版本**: 功能完整，稳定可靠，推荐使用
✅ **GUI 版本**: 界面友好，功能丰富（需要 PyQt6 支持）
✅ **核心功能**: 多浏览器并发、智能自动化、实时监控
✅ **配置管理**: 灵活的参数设置和浏览器管理
✅ **错误处理**: 完善的异常处理和日志记录

立即开始使用：
```bash
python check_environment.py  # 检查环境
python main_console.py       # 启动控制台版本
```

🎉 **项目已准备就绪，开始您的 YouTube Shorts 自动化之旅！**
