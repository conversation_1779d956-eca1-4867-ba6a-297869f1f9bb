"""
控制台日志设置工具
不依赖 PyQt6 的日志系统配置
"""

import logging
import sys
from typing import Optional
from pathlib import Path


class ConsoleLoggerSetup:
    """控制台日志设置管理器"""
    
    def __init__(self):
        self.console_handler = None
        self.file_handler = None
    
    def setup_logging(self, 
                     level: int = logging.INFO,
                     log_file: Optional[str] = None) -> logging.Logger:
        """
        设置应用程序日志系统
        
        Args:
            level: 日志级别
            log_file: 日志文件路径（可选）
            
        Returns:
            logging.Logger: 根日志器
        """
        # 获取根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(level)
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        
        # 控制台处理器
        self.console_handler = logging.StreamHandler(sys.stdout)
        self.console_handler.setLevel(level)
        self.console_handler.setFormatter(formatter)
        root_logger.addHandler(self.console_handler)
        
        # 文件处理器（如果指定了文件路径）
        if log_file:
            try:
                # 确保日志目录存在
                log_path = Path(log_file)
                log_path.parent.mkdir(parents=True, exist_ok=True)
                
                self.file_handler = logging.FileHandler(
                    log_file, 
                    mode='a', 
                    encoding='utf-8'
                )
                self.file_handler.setLevel(level)
                self.file_handler.setFormatter(formatter)
                root_logger.addHandler(self.file_handler)
            except Exception as e:
                print(f"无法创建日志文件处理器: {e}")
        
        # 设置特定模块的日志级别
        self._configure_module_loggers()
        
        logging.info("控制台日志系统初始化完成")
        return root_logger
    
    def _configure_module_loggers(self):
        """配置特定模块的日志级别"""
        # Playwright 日志级别设置为 WARNING 以减少噪音
        logging.getLogger("playwright").setLevel(logging.WARNING)
        
        # urllib3 日志级别设置为 WARNING
        logging.getLogger("urllib3").setLevel(logging.WARNING)
        
        # requests 日志级别设置为 WARNING
        logging.getLogger("requests").setLevel(logging.WARNING)
    
    def set_log_level(self, level: int):
        """
        设置日志级别
        
        Args:
            level: 新的日志级别
        """
        root_logger = logging.getLogger()
        root_logger.setLevel(level)
        
        if self.console_handler:
            self.console_handler.setLevel(level)
        
        if self.file_handler:
            self.file_handler.setLevel(level)
    
    def clear_handlers(self):
        """清除所有日志处理器"""
        root_logger = logging.getLogger()
        root_logger.handlers.clear()


# 全局日志设置实例
console_logger_setup = ConsoleLoggerSetup()


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        logging.Logger: 日志器实例
    """
    return logging.getLogger(name)
