#!/usr/bin/env python3
"""
YouTube Shorts 机器人控制台版本
无 GUI 的命令行版本，用于在 PyQt6 不可用时运行
"""

import sys
import os
import asyncio
import json
import logging
import time
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.browser_api import ixBrowserAPI
from src.youtube_bot import YouTubeBot
from utils.config_manager import ConfigManager
from utils.console_logger import ConsoleLoggerSetup


class ConsoleApp:
    """控制台应用程序"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.browser_api = ixBrowserAPI()
        self.running_bots = {}
        self.logger = None
        
        # 设置日志
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志系统"""
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 设置日志文件
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"youtube_bot_console_{timestamp}.log"
        
        # 初始化日志系统
        logger_setup = ConsoleLoggerSetup()
        logger_setup.setup_logging(
            level=logging.INFO,
            log_file=str(log_file)
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("=" * 50)
        self.logger.info("YouTube Shorts 机器人控制台版本启动")
        self.logger.info("=" * 50)
    
    def check_environment(self):
        """检查运行环境"""
        print("检查运行环境...")
        
        # 检查 API 服务
        if not self.browser_api.check_api_server():
            print("✗ ixBrowser API 服务不可用")
            print("  请确保 ixBrowser 软件正在运行")
            return False
        
        print("✓ ixBrowser API 服务正常")
        
        # 检查配置文件
        try:
            config = self.config_manager.load_config()
            browsers = config.get("browsers", [])
            if not browsers:
                print("⚠ 没有配置浏览器，请先添加浏览器配置")
            else:
                print(f"✓ 找到 {len(browsers)} 个浏览器配置")
        except Exception as e:
            print(f"✗ 配置文件错误: {e}")
            return False
        
        return True
    
    def show_menu(self):
        """显示主菜单"""
        print("\n" + "="*50)
        print("YouTube Shorts 机器人 - 控制台版本")
        print("="*50)
        print("1. 查看浏览器配置")
        print("2. 添加浏览器配置")
        print("3. 删除浏览器配置")
        print("4. 查看运行设置")
        print("5. 修改运行设置")
        print("6. 开始自动化任务")
        print("7. 查看运行状态")
        print("8. 停止所有任务")
        print("0. 退出程序")
        print("="*50)
    
    def show_browsers(self):
        """显示浏览器配置"""
        browsers = self.config_manager.get_browsers()
        
        if not browsers:
            print("没有配置的浏览器")
            return
        
        print(f"\n当前浏览器配置 ({len(browsers)} 个):")
        print("-" * 40)
        for i, browser in enumerate(browsers, 1):
            status = "启用" if browser.get("enabled", True) else "禁用"
            print(f"{i}. {browser.get('name', '未知')} ({browser.get('id', '未知')}) - {status}")
    
    def add_browser(self):
        """添加浏览器配置"""
        print("\n添加浏览器配置:")
        
        browser_id = input("请输入浏览器 ID: ").strip()
        if not browser_id:
            print("浏览器 ID 不能为空")
            return
        
        name = input("请输入显示名称: ").strip()
        if not name:
            print("显示名称不能为空")
            return
        
        if self.config_manager.add_browser(browser_id, name, True):
            print(f"✓ 浏览器 '{name}' 添加成功")
        else:
            print("✗ 添加失败，可能 ID 已存在")
    
    def remove_browser(self):
        """删除浏览器配置"""
        self.show_browsers()
        browsers = self.config_manager.get_browsers()
        
        if not browsers:
            return
        
        try:
            index = int(input("\n请选择要删除的浏览器编号: ")) - 1
            if 0 <= index < len(browsers):
                browser = browsers[index]
                if self.config_manager.remove_browser(browser["id"]):
                    print(f"✓ 浏览器 '{browser['name']}' 删除成功")
                else:
                    print("✗ 删除失败")
            else:
                print("无效的编号")
        except ValueError:
            print("请输入有效的数字")
    
    def show_settings(self):
        """显示运行设置"""
        settings = self.config_manager.get_settings()
        
        print("\n当前运行设置:")
        print("-" * 30)
        print(f"最小观看时间: {settings.get('min_watch_time', 3)} 秒")
        print(f"最大观看时间: {settings.get('max_watch_time', 8)} 秒")
        print(f"点赞概率: {settings.get('like_probability', 0.3)}")
        print(f"订阅概率: {settings.get('subscribe_probability', 0.1)}")
        print(f"视频数量: {settings.get('scroll_count', 50)} 个")
    
    def modify_settings(self):
        """修改运行设置"""
        settings = self.config_manager.get_settings()
        
        print("\n修改运行设置 (直接回车保持当前值):")
        
        try:
            # 观看时间
            min_time = input(f"最小观看时间 (当前: {settings.get('min_watch_time', 3)}): ").strip()
            if min_time:
                settings['min_watch_time'] = int(min_time)
            
            max_time = input(f"最大观看时间 (当前: {settings.get('max_watch_time', 8)}): ").strip()
            if max_time:
                settings['max_watch_time'] = int(max_time)
            
            # 概率设置
            like_prob = input(f"点赞概率 (当前: {settings.get('like_probability', 0.3)}): ").strip()
            if like_prob:
                settings['like_probability'] = float(like_prob)
            
            sub_prob = input(f"订阅概率 (当前: {settings.get('subscribe_probability', 0.1)}): ").strip()
            if sub_prob:
                settings['subscribe_probability'] = float(sub_prob)
            
            # 视频数量
            count = input(f"视频数量 (当前: {settings.get('scroll_count', 50)}): ").strip()
            if count:
                settings['scroll_count'] = int(count)
            
            # 保存设置
            if self.config_manager.update_settings(settings):
                print("✓ 设置保存成功")
            else:
                print("✗ 设置保存失败")
                
        except ValueError:
            print("✗ 输入格式错误")
    
    async def start_automation(self):
        """开始自动化任务"""
        browsers = self.config_manager.get_browsers()
        enabled_browsers = [b for b in browsers if b.get("enabled", True)]
        
        if not enabled_browsers:
            print("没有启用的浏览器配置")
            return
        
        settings = self.config_manager.get_settings()
        
        print(f"\n准备启动 {len(enabled_browsers)} 个机器人...")
        print("设置信息:")
        print(f"  观看时间: {settings.get('min_watch_time', 3)}-{settings.get('max_watch_time', 8)} 秒")
        print(f"  点赞概率: {settings.get('like_probability', 0.3)}")
        print(f"  订阅概率: {settings.get('subscribe_probability', 0.1)}")
        print(f"  视频数量: {settings.get('scroll_count', 50)} 个")
        
        confirm = input("\n确认开始? (y/N): ").strip().lower()
        if confirm != 'y':
            print("已取消")
            return
        
        # 启动机器人
        tasks = []
        for browser_data in enabled_browsers:
            browser_id = browser_data["id"]
            
            # 打开浏览器
            print(f"正在打开浏览器: {browser_data['name']}")
            ws_endpoint = self.browser_api.open_browser(browser_id)
            
            if ws_endpoint:
                # 创建机器人任务
                bot = YouTubeBot(browser_id, ws_endpoint)
                task = asyncio.create_task(self.run_bot(bot, settings, browser_data['name']))
                tasks.append(task)
                self.running_bots[browser_id] = {
                    'bot': bot,
                    'task': task,
                    'name': browser_data['name']
                }
                print(f"✓ 机器人 {browser_data['name']} 启动成功")
            else:
                print(f"✗ 无法打开浏览器: {browser_data['name']}")
        
        if tasks:
            print(f"\n{len(tasks)} 个机器人开始运行...")
            print("按 Ctrl+C 停止所有任务")
            
            try:
                # 等待所有任务完成
                await asyncio.gather(*tasks, return_exceptions=True)
                print("\n所有任务已完成")
            except KeyboardInterrupt:
                print("\n收到停止信号，正在停止所有机器人...")
                await self.stop_all_bots()
        else:
            print("没有成功启动任何机器人")
    
    async def run_bot(self, bot: YouTubeBot, settings: Dict[str, Any], name: str):
        """运行单个机器人"""
        try:
            # 初始化浏览器
            if not await bot.init_browser():
                print(f"✗ 机器人 {name} 初始化失败")
                return
            
            print(f"✓ 机器人 {name} 初始化成功，开始运行...")
            
            # 运行机器人
            final_stats = await bot.run(settings)
            
            print(f"✓ 机器人 {name} 运行完成")
            print(f"  统计: 观看 {final_stats.get('videos_watched', 0)}, "
                  f"点赞 {final_stats.get('likes_given', 0)}, "
                  f"订阅 {final_stats.get('subscriptions_made', 0)}")
            
        except Exception as e:
            print(f"✗ 机器人 {name} 运行错误: {e}")
            self.logger.error(f"机器人 {name} 运行错误: {e}")
        finally:
            await bot.cleanup()
    
    def show_status(self):
        """显示运行状态"""
        if not self.running_bots:
            print("当前没有运行中的机器人")
            return
        
        print(f"\n运行状态 ({len(self.running_bots)} 个机器人):")
        print("-" * 50)
        
        for browser_id, bot_info in self.running_bots.items():
            name = bot_info['name']
            task = bot_info['task']
            bot = bot_info['bot']
            
            if task.done():
                status = "已完成"
            elif bot.running:
                status = "运行中"
                stats = bot.get_stats()
                status += f" (观看: {stats.get('videos_watched', 0)}, "
                status += f"点赞: {stats.get('likes_given', 0)}, "
                status += f"订阅: {stats.get('subscriptions_made', 0)})"
            else:
                status = "已停止"
            
            print(f"{name} ({browser_id}): {status}")
    
    async def stop_all_bots(self):
        """停止所有机器人"""
        if not self.running_bots:
            print("没有运行中的机器人")
            return
        
        print("正在停止所有机器人...")
        
        # 停止所有机器人
        for browser_id, bot_info in self.running_bots.items():
            bot = bot_info['bot']
            bot.stop()
            
            # 关闭浏览器
            self.browser_api.close_browser(browser_id)
        
        # 等待任务完成
        tasks = [bot_info['task'] for bot_info in self.running_bots.values()]
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        
        self.running_bots.clear()
        print("✓ 所有机器人已停止")
    
    async def run(self):
        """运行主程序"""
        if not self.check_environment():
            return
        
        while True:
            try:
                self.show_menu()
                choice = input("\n请选择操作: ").strip()
                
                if choice == "0":
                    if self.running_bots:
                        await self.stop_all_bots()
                    print("程序退出")
                    break
                elif choice == "1":
                    self.show_browsers()
                elif choice == "2":
                    self.add_browser()
                elif choice == "3":
                    self.remove_browser()
                elif choice == "4":
                    self.show_settings()
                elif choice == "5":
                    self.modify_settings()
                elif choice == "6":
                    await self.start_automation()
                elif choice == "7":
                    self.show_status()
                elif choice == "8":
                    await self.stop_all_bots()
                else:
                    print("无效的选择")
                
                if choice != "6":  # 自动化任务会阻塞，不需要暂停
                    input("\n按 Enter 键继续...")
                
            except KeyboardInterrupt:
                print("\n\n收到中断信号...")
                if self.running_bots:
                    await self.stop_all_bots()
                break
            except Exception as e:
                print(f"发生错误: {e}")
                self.logger.error(f"程序错误: {e}")


async def main():
    """主函数"""
    app = ConsoleApp()
    await app.run()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序启动失败: {e}")
        sys.exit(1)
