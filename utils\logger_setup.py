"""
日志设置工具
配置应用程序的日志系统
"""

import logging
import sys
from typing import Optional
from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtWidgets import QTextEdit


class QTextEditHandler(logging.Handler, QObject):
    """自定义日志处理器，将日志输出到 QTextEdit"""
    
    # 定义信号，用于线程安全的日志更新
    log_signal = pyqtSignal(str)
    
    def __init__(self, text_edit: Optional[QTextEdit] = None):
        """
        初始化处理器
        
        Args:
            text_edit: 目标 QTextEdit 控件
        """
        logging.Handler.__init__(self)
        QObject.__init__(self)
        
        self.text_edit = text_edit
        if text_edit:
            self.log_signal.connect(self._append_log)
    
    def set_text_edit(self, text_edit: QTextEdit):
        """
        设置目标 QTextEdit 控件
        
        Args:
            text_edit: 目标 QTextEdit 控件
        """
        self.text_edit = text_edit
        self.log_signal.connect(self._append_log)
    
    def emit(self, record):
        """
        发出日志记录
        
        Args:
            record: 日志记录对象
        """
        try:
            msg = self.format(record)
            # 使用信号确保线程安全
            self.log_signal.emit(msg)
        except Exception:
            self.handleError(record)
    
    def _append_log(self, message: str):
        """
        将日志消息添加到 QTextEdit
        
        Args:
            message: 日志消息
        """
        if self.text_edit:
            # 添加时间戳和换行
            self.text_edit.append(message)
            # 自动滚动到底部
            cursor = self.text_edit.textCursor()
            cursor.movePosition(cursor.MoveOperation.End)
            self.text_edit.setTextCursor(cursor)


class LoggerSetup:
    """日志设置管理器"""
    
    def __init__(self):
        self.qt_handler = None
        self.console_handler = None
        self.file_handler = None
    
    def setup_logging(self, 
                     level: int = logging.INFO,
                     log_file: Optional[str] = None,
                     text_edit: Optional[QTextEdit] = None) -> QTextEditHandler:
        """
        设置应用程序日志系统
        
        Args:
            level: 日志级别
            log_file: 日志文件路径（可选）
            text_edit: QTextEdit 控件（可选）
            
        Returns:
            QTextEditHandler: QTextEdit 处理器实例
        """
        # 获取根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(level)
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        
        # 控制台处理器
        self.console_handler = logging.StreamHandler(sys.stdout)
        self.console_handler.setLevel(level)
        self.console_handler.setFormatter(formatter)
        root_logger.addHandler(self.console_handler)
        
        # 文件处理器（如果指定了文件路径）
        if log_file:
            try:
                self.file_handler = logging.FileHandler(
                    log_file, 
                    mode='a', 
                    encoding='utf-8'
                )
                self.file_handler.setLevel(level)
                self.file_handler.setFormatter(formatter)
                root_logger.addHandler(self.file_handler)
            except Exception as e:
                print(f"无法创建日志文件处理器: {e}")
        
        # QTextEdit 处理器
        self.qt_handler = QTextEditHandler(text_edit)
        self.qt_handler.setLevel(level)
        
        # 为 GUI 使用简化的格式器
        gui_formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            datefmt='%H:%M:%S'
        )
        self.qt_handler.setFormatter(gui_formatter)
        root_logger.addHandler(self.qt_handler)
        
        # 设置特定模块的日志级别
        self._configure_module_loggers()
        
        logging.info("日志系统初始化完成")
        return self.qt_handler
    
    def _configure_module_loggers(self):
        """配置特定模块的日志级别"""
        # Playwright 日志级别设置为 WARNING 以减少噪音
        logging.getLogger("playwright").setLevel(logging.WARNING)
        
        # urllib3 日志级别设置为 WARNING
        logging.getLogger("urllib3").setLevel(logging.WARNING)
        
        # requests 日志级别设置为 WARNING
        logging.getLogger("requests").setLevel(logging.WARNING)
    
    def set_log_level(self, level: int):
        """
        设置日志级别
        
        Args:
            level: 新的日志级别
        """
        root_logger = logging.getLogger()
        root_logger.setLevel(level)
        
        if self.console_handler:
            self.console_handler.setLevel(level)
        
        if self.file_handler:
            self.file_handler.setLevel(level)
        
        if self.qt_handler:
            self.qt_handler.setLevel(level)
    
    def get_qt_handler(self) -> Optional[QTextEditHandler]:
        """
        获取 QTextEdit 处理器
        
        Returns:
            Optional[QTextEditHandler]: QTextEdit 处理器实例
        """
        return self.qt_handler
    
    def clear_handlers(self):
        """清除所有日志处理器"""
        root_logger = logging.getLogger()
        root_logger.handlers.clear()


# 全局日志设置实例
logger_setup = LoggerSetup()


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        logging.Logger: 日志器实例
    """
    return logging.getLogger(name)
