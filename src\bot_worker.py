"""
机器人工作线程
在后台运行 YouTube 机器人，防止 GUI 冻结
"""

import asyncio
import logging
from typing import Dict, Any
from PyQt6.QtCore import QThread, pyqtSignal
from src.youtube_bot import YouTubeBot

logger = logging.getLogger(__name__)


class BotWorker(QThread):
    """机器人工作线程"""
    
    # 定义信号
    status_updated = pyqtSignal(str, str, dict)  # browser_id, status, stats
    error_occurred = pyqtSignal(str, str)        # browser_id, error_message
    finished_signal = pyqtSignal(str, dict)      # browser_id, final_stats
    
    def __init__(self, browser_id: str, ws_endpoint: str, settings: Dict[str, Any]):
        """
        初始化工作线程
        
        Args:
            browser_id: 浏览器 ID
            ws_endpoint: WebSocket 端点
            settings: 运行设置
        """
        super().__init__()
        self.browser_id = browser_id
        self.ws_endpoint = ws_endpoint
        self.settings = settings
        self.bot: YouTubeBot = None
        self._stop_requested = False
    
    def run(self):
        """运行工作线程"""
        try:
            logger.info(f"启动机器人工作线程: {self.browser_id}")
            
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # 运行异步任务
                final_stats = loop.run_until_complete(self._run_bot())
                
                # 发送完成信号
                self.finished_signal.emit(self.browser_id, final_stats)
                
            finally:
                loop.close()
                
        except Exception as e:
            error_msg = f"工作线程运行失败: {e}"
            logger.error(f"浏览器 {self.browser_id} - {error_msg}")
            self.error_occurred.emit(self.browser_id, error_msg)
    
    async def _run_bot(self) -> Dict[str, int]:
        """
        运行机器人的异步方法
        
        Returns:
            Dict[str, int]: 最终统计数据
        """
        try:
            # 创建机器人实例
            self.bot = YouTubeBot(self.browser_id, self.ws_endpoint)
            
            # 发送初始化状态
            self.status_updated.emit(self.browser_id, "正在初始化...", {})
            
            # 初始化浏览器
            if not await self.bot.init_browser():
                raise Exception("浏览器初始化失败")
            
            # 发送运行状态
            self.status_updated.emit(self.browser_id, "正在运行", self.bot.get_stats())
            
            # 运行机器人主循环
            final_stats = await self._run_with_status_updates()
            
            # 发送完成状态
            self.status_updated.emit(self.browser_id, "已完成", final_stats)
            
            return final_stats
            
        except Exception as e:
            error_msg = f"机器人运行失败: {e}"
            logger.error(f"浏览器 {self.browser_id} - {error_msg}")
            self.error_occurred.emit(self.browser_id, error_msg)
            return {}
        
        finally:
            # 清理资源
            if self.bot:
                await self.bot.cleanup()
    
    async def _run_with_status_updates(self) -> Dict[str, int]:
        """
        运行机器人并定期更新状态
        
        Returns:
            Dict[str, int]: 最终统计数据
        """
        # 创建状态更新任务
        status_task = asyncio.create_task(self._status_update_loop())
        
        try:
            # 运行机器人
            final_stats = await self.bot.run(self.settings)
            return final_stats
            
        finally:
            # 取消状态更新任务
            status_task.cancel()
            try:
                await status_task
            except asyncio.CancelledError:
                pass
    
    async def _status_update_loop(self):
        """状态更新循环"""
        try:
            while self.bot and self.bot.running:
                # 发送状态更新
                current_stats = self.bot.get_stats()
                self.status_updated.emit(self.browser_id, "正在运行", current_stats)
                
                # 等待一段时间再更新
                await asyncio.sleep(2)  # 每2秒更新一次状态
                
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"状态更新循环错误: {e}")
    
    def stop(self):
        """停止机器人"""
        logger.info(f"请求停止机器人: {self.browser_id}")
        self._stop_requested = True
        
        if self.bot:
            self.bot.stop()
    
    def is_running(self) -> bool:
        """
        检查机器人是否正在运行
        
        Returns:
            bool: 是否正在运行
        """
        return self.bot is not None and self.bot.running


class BotManager:
    """机器人管理器，管理多个工作线程"""
    
    def __init__(self):
        self.workers: Dict[str, BotWorker] = {}
    
    def start_bot(self, browser_id: str, ws_endpoint: str, settings: Dict[str, Any]) -> BotWorker:
        """
        启动机器人
        
        Args:
            browser_id: 浏览器 ID
            ws_endpoint: WebSocket 端点
            settings: 运行设置
            
        Returns:
            BotWorker: 工作线程实例
        """
        # 如果已存在，先停止
        if browser_id in self.workers:
            self.stop_bot(browser_id)
        
        # 创建新的工作线程
        worker = BotWorker(browser_id, ws_endpoint, settings)
        self.workers[browser_id] = worker
        
        # 启动线程
        worker.start()
        
        logger.info(f"启动机器人: {browser_id}")
        return worker
    
    def stop_bot(self, browser_id: str):
        """
        停止指定的机器人
        
        Args:
            browser_id: 浏览器 ID
        """
        if browser_id in self.workers:
            worker = self.workers[browser_id]
            worker.stop()
            
            # 等待线程结束
            if worker.isRunning():
                worker.wait(5000)  # 等待最多5秒
            
            del self.workers[browser_id]
            logger.info(f"停止机器人: {browser_id}")
    
    def stop_all_bots(self):
        """停止所有机器人"""
        browser_ids = list(self.workers.keys())
        for browser_id in browser_ids:
            self.stop_bot(browser_id)
        
        logger.info("停止所有机器人")
    
    def get_running_bots(self) -> Dict[str, BotWorker]:
        """
        获取正在运行的机器人
        
        Returns:
            Dict[str, BotWorker]: 正在运行的机器人字典
        """
        return {
            browser_id: worker 
            for browser_id, worker in self.workers.items() 
            if worker.is_running()
        }
    
    def get_bot_count(self) -> int:
        """
        获取机器人数量
        
        Returns:
            int: 机器人数量
        """
        return len(self.workers)
