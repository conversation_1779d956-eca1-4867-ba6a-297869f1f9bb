# 顺序自动化任务使用指南

## 🎯 功能概述

`sequential_automation.py` 是一个按照配置文件顺序执行的自动化任务脚本，具有以下特点：

- **顺序执行**: 每次只运行一个浏览器，完成后再运行下一个（非并发）
- **随机视频数量**: 每个浏览器的 `scroll_count` 在 50-100 次之间随机生成
- **完整统计**: 提供详细的执行计划、进度显示和最终统计
- **智能管理**: 自动打开/关闭浏览器，完善的错误处理

## 🚀 快速开始

### 1. 运行脚本
```bash
python sequential_automation.py
```

### 2. 查看执行计划
脚本会自动显示详细的执行计划：

```
============================================================
📋 执行计划
============================================================
执行模式: 顺序执行 (一个接一个)
浏览器数量: 7 个
观看时间: 3-8 秒
点赞概率: 0.3 (30%)
订阅概率: 0.1 (10%)
滚动次数: 50-100 次 (随机)

📝 浏览器执行顺序:
  1. 乡村3 (ID: 4) - 81 个视频
  2. 长视频1 (ID: 7) - 80 个视频
  3. YouTube_猫咪 (ID: 1) - 89 个视频
  4. 乡村1 (ID: 2) - 60 个视频
  5. 乡村2 (ID: 3) - 58 个视频
  6. 乡村4 (ID: 5) - 50 个视频
  7. 故事1 (ID: 6) - 79 个视频

⏱️ 预估总时间: 66 分钟
============================================================
```

### 3. 确认执行
```
确认开始顺序执行 7 个浏览器的自动化任务? (y/N): y
```

## 📊 执行过程

### 实时进度显示
```
============================================================
📍 进度: 1/7
============================================================

🚀 开始执行: 乡村3 (ID: 4)
📊 目标视频数: 72 个
🔗 正在打开浏览器...
✅ 浏览器打开成功
🔧 正在初始化浏览器连接...
✅ 浏览器初始化成功
🎬 开始自动化任务...
```

### 详细日志记录
```
11:16:39 - 开始执行浏览器: 乡村3 (ID: 4), 目标视频: 72
11:16:39 - 浏览器 4 打开成功，WebSocket: ws://127.0.0.1:54279/...
11:16:52 - 机器人 4 处理第 1/72 个视频
11:17:02 - 机器人 4 完成视频观看 #1
11:17:03 - 机器人 4 处理第 2/72 个视频
11:17:08 - 机器人 4 完成点赞 #1
```

### 单个浏览器完成统计
```
✅ 乡村3 任务完成!
📊 统计结果:
   - 观看视频: 72 个
   - 点赞次数: 23 次
   - 订阅次数: 7 次
   - 错误次数: 0 次
   - 执行时间: 8.5 分钟
```

## 🎲 随机化特性

### 视频数量随机化
- **基础设置**: 配置文件中的 `scroll_count` 作为参考值
- **随机范围**: 50-100 个视频之间
- **实时生成**: 每个浏览器开始执行时重新生成随机数

### 等待时间随机化
- **浏览器间隔**: 每个浏览器完成后等待 5-15 秒再开始下一个
- **观看时间**: 根据配置的 `min_watch_time` 和 `max_watch_time` 随机
- **操作延迟**: 点赞、订阅等操作有随机延迟

## 📈 最终统计报告

任务完成后会显示详细的总体统计：

```
============================================================
🎉 所有任务执行完成!
============================================================
📊 总体统计:
   - 完成浏览器: 7 个
   - 总观看视频: 497 个
   - 总点赞次数: 152 次
   - 总订阅次数: 48 次
   - 总错误次数: 3 次
   - 总执行时间: 58.3 分钟
   - 平均每浏览器: 8.3 分钟
   - 平均每视频: 7.0 秒

⏰ 结束时间: 2025-07-12 12:15:23
============================================================
```

## 🔧 配置要求

### 配置文件结构
确保 `config/config.json` 包含以下结构：

```json
{
  "browsers": [
    {
      "id": "4",
      "name": "乡村3",
      "enabled": true
    },
    {
      "id": "7", 
      "name": "长视频1",
      "enabled": true
    }
  ],
  "settings": {
    "min_watch_time": 3,
    "max_watch_time": 8,
    "like_probability": 0.3,
    "subscribe_probability": 0.1,
    "scroll_count": 50
  }
}
```

### 环境要求
- ✅ ixBrowser 软件正在运行
- ✅ API 服务在 `http://127.0.0.1:53200` 可用
- ✅ 至少有一个启用的浏览器配置
- ✅ 浏览器 ID 在 ixBrowser 中存在

## 📝 日志文件

### 自动生成日志
- **文件位置**: `logs/sequential_automation_YYYYMMDD_HHMMSS.log`
- **内容包含**: 详细的执行过程、错误信息、统计数据
- **格式**: 时间戳 + 日志级别 + 消息内容

### 日志示例
```
11:16:07 - INFO - 顺序自动化任务执行器启动
11:16:35 - INFO - 开始执行浏览器: 乡村3 (ID: 4), 目标视频: 72
11:16:39 - INFO - 浏览器 4 打开成功
11:17:02 - INFO - 机器人 4 完成视频观看 #1
```

## ⚠️ 注意事项

### 执行前检查
1. **确认 ixBrowser 运行**: 检查 API 服务状态
2. **验证浏览器配置**: 确保所有启用的浏览器 ID 存在
3. **检查网络连接**: 确保能够访问 YouTube
4. **预留足够时间**: 根据预估时间安排执行

### 中断处理
- **Ctrl+C**: 可以随时中断任务
- **优雅停止**: 会完成当前视频后停止
- **资源清理**: 自动关闭浏览器和清理连接

### 错误恢复
- **连接断开**: 自动检测并停止当前浏览器
- **浏览器故障**: 记录错误并继续下一个浏览器
- **API 错误**: 详细记录错误信息便于排查

## 🔄 与其他版本对比

| 特性 | 顺序执行版本 | 并发执行版本 | 控制台版本 |
|------|-------------|-------------|-----------|
| 执行方式 | 一个接一个 | 同时运行多个 | 手动控制 |
| 资源占用 | 低 | 高 | 低 |
| 执行时间 | 长 | 短 | 可控 |
| 稳定性 | 高 | 中等 | 高 |
| 监控难度 | 简单 | 复杂 | 简单 |

## 🎯 使用建议

### 适用场景
- **大量浏览器**: 需要处理很多浏览器但不想同时运行
- **资源限制**: 系统资源有限，无法支持并发
- **稳定优先**: 优先考虑稳定性而非速度
- **无人值守**: 需要长时间自动运行

### 最佳实践
1. **合理设置概率**: 避免过高的点赞/订阅概率
2. **监控日志**: 定期查看日志文件了解执行状态
3. **分批执行**: 如果浏览器很多，可以分批运行
4. **测试先行**: 先用少量浏览器测试再大规模执行

## 🚀 立即开始

```bash
# 1. 确保环境正常
python check_environment.py

# 2. 运行顺序自动化
python sequential_automation.py

# 3. 确认执行计划后输入 'y' 开始
```

**享受自动化带来的便利！** 🎉
