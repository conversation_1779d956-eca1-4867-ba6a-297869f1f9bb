#!/usr/bin/env python3
"""
核心功能测试脚本（不包含 GUI 测试）
用于验证应用程序的核心组件是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_core_imports():
    """测试核心模块导入（不包含 GUI）"""
    print("测试核心模块导入...")
    
    try:
        # 测试核心模块
        from src.browser_api import ixBrowserAPI
        print("✓ ixBrowserAPI 导入成功")
        
        from utils.config_manager import ConfigManager
        print("✓ ConfigManager 导入成功")
        
        # 测试非 GUI 的 YouTube 机器人
        try:
            from src.youtube_bot import YouTubeBot
            print("✓ YouTubeBot 导入成功")
        except Exception as e:
            print(f"⚠ YouTubeBot 导入失败: {e} (可能需要 Playwright)")
        
        return True
        
    except ImportError as e:
        print(f"✗ 核心模块导入失败: {e}")
        return False


def test_config_manager():
    """测试配置管理器"""
    print("\n测试配置管理器...")
    
    try:
        from utils.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        
        # 测试加载配置
        config = config_manager.load_config()
        print(f"✓ 配置加载成功: {len(config)} 个配置项")
        
        # 测试获取设置
        settings = config_manager.get_settings()
        required_settings = ["min_watch_time", "max_watch_time", "like_probability", "subscribe_probability", "scroll_count"]
        
        for setting in required_settings:
            if setting in settings:
                print(f"✓ 设置项 '{setting}' 存在: {settings[setting]}")
            else:
                print(f"✗ 设置项 '{setting}' 缺失")
                return False
        
        # 测试获取浏览器列表
        browsers = config_manager.get_browsers()
        print(f"✓ 浏览器列表获取成功: {len(browsers)} 个浏览器")
        
        # 测试 XPath 配置
        xpath_config = config_manager.load_xpath_config()
        print(f"✓ XPath 配置加载成功: {len(xpath_config)} 个配置项")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置管理器测试失败: {e}")
        return False


def test_browser_api():
    """测试浏览器 API"""
    print("\n测试浏览器 API...")
    
    try:
        from src.browser_api import ixBrowserAPI
        
        api = ixBrowserAPI()
        
        # 测试 API 服务检查
        print("正在检查 ixBrowser API 服务...")
        is_available = api.check_api_server()
        if is_available:
            print("✓ ixBrowser API 服务可用")
            
            # 测试获取浏览器列表
            browser_list = api.get_browser_list()
            if browser_list is not None:
                print("✓ 浏览器列表获取成功")
                if 'list' in browser_list:
                    print(f"  - 找到 {len(browser_list['list'])} 个浏览器配置")
            else:
                print("⚠ 浏览器列表获取失败（可能是正常的）")
        else:
            print("⚠ ixBrowser API 服务不可用")
            print("  提示: 请确保 ixBrowser 软件正在运行")
        
        return True
        
    except Exception as e:
        print(f"✗ 浏览器 API 测试失败: {e}")
        return False


def test_file_structure():
    """测试文件结构"""
    print("\n测试文件结构...")
    
    required_files = [
        "main.py",
        "requirements.txt",
        "config/config.json",
        "config/xpath_config.json",
        "src/__init__.py",
        "src/browser_api.py",
        "src/youtube_bot.py",
        "src/bot_worker.py",
        "src/main_window.py",
        "src/gui/__init__.py",
        "src/gui/browser_panel.py",
        "src/gui/config_panel.py",
        "src/gui/status_panel.py",
        "src/gui/log_panel.py",
        "utils/__init__.py",
        "utils/config_manager.py",
        "utils/logger_setup.py"
    ]
    
    missing_files = []
    existing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            existing_files.append(file_path)
        else:
            missing_files.append(file_path)
    
    print(f"✓ 存在的文件: {len(existing_files)}/{len(required_files)}")
    
    if missing_files:
        print(f"✗ 缺少文件:")
        for file in missing_files:
            print(f"  - {file}")
        return False
    else:
        print(f"✓ 所有必需文件都存在")
        return True


def test_config_files():
    """测试配置文件内容"""
    print("\n测试配置文件内容...")
    
    try:
        import json
        
        # 测试主配置文件
        with open("config/config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        required_keys = ["browsers", "settings", "api"]
        for key in required_keys:
            if key in config:
                print(f"✓ 主配置包含 '{key}' 部分")
            else:
                print(f"✗ 主配置缺少 '{key}' 部分")
                return False
        
        # 测试 XPath 配置文件
        with open("config/xpath_config.json", "r", encoding="utf-8") as f:
            xpath_config = json.load(f)
        
        required_xpath_keys = ["selectors", "wait_times"]
        for key in required_xpath_keys:
            if key in xpath_config:
                print(f"✓ XPath 配置包含 '{key}' 部分")
            else:
                print(f"✗ XPath 配置缺少 '{key}' 部分")
                return False
        
        # 检查选择器
        selectors = xpath_config.get("selectors", {})
        required_selectors = ["like_button", "subscribe_button"]
        for selector in required_selectors:
            if selector in selectors and selectors[selector]:
                print(f"✓ 选择器 '{selector}' 已配置 ({len(selectors[selector])} 个)")
            else:
                print(f"✗ 选择器 '{selector}' 未配置或为空")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 配置文件测试失败: {e}")
        return False


def test_dependencies():
    """测试依赖项"""
    print("\n测试依赖项...")
    
    dependencies = [
        ("requests", "HTTP 客户端"),
        ("json", "JSON 处理"),
        ("logging", "日志系统"),
        ("pathlib", "路径处理"),
    ]
    
    missing_deps = []
    
    for dep, desc in dependencies:
        try:
            __import__(dep)
            print(f"✓ {desc} ({dep}) 可用")
        except ImportError:
            print(f"✗ {desc} ({dep}) 不可用")
            missing_deps.append(dep)
    
    # 测试可选依赖
    optional_deps = [
        ("playwright", "浏览器自动化"),
        ("PyQt6", "GUI 框架"),
    ]
    
    for dep, desc in optional_deps:
        try:
            __import__(dep)
            print(f"✓ {desc} ({dep}) 可用")
        except ImportError:
            print(f"⚠ {desc} ({dep}) 不可用 (可选)")
    
    return len(missing_deps) == 0


def main():
    """主测试函数"""
    print("YouTube Shorts 机器人 - 核心功能测试")
    print("=" * 50)
    
    tests = [
        ("文件结构", test_file_structure),
        ("配置文件", test_config_files),
        ("依赖项", test_dependencies),
        ("核心模块导入", test_core_imports),
        ("配置管理器", test_config_manager),
        ("浏览器 API", test_browser_api),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{passed + 1}/{total}] {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有核心功能测试通过！")
        print("\n下一步:")
        print("1. 安装 PyQt6: pip install PyQt6")
        print("2. 安装 Playwright: pip install playwright && playwright install chromium")
        print("3. 启动 ixBrowser 软件")
        print("4. 运行应用程序: python main.py")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关组件。")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
