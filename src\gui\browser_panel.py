"""
浏览器管理面板
用于管理浏览器配置文件
"""

import logging
from typing import List, Dict, Any
from PyQt6.QtWidgets import (
    QGroupBox, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QPushButton, QCheckBox, QDialog, QDialogButtonBox, QFormLayout,
    QLineEdit, QMessageBox, QWidget, QLabel
)
from PyQt6.QtCore import Qt, pyqtSignal
from utils.config_manager import ConfigManager

logger = logging.getLogger(__name__)


class BrowserDialog(QDialog):
    """浏览器添加/编辑对话框"""
    
    def __init__(self, parent=None, browser_data=None):
        """
        初始化对话框
        
        Args:
            parent: 父窗口
            browser_data: 浏览器数据（编辑模式）
        """
        super().__init__(parent)
        self.browser_data = browser_data
        self.is_edit_mode = browser_data is not None
        
        self.setWindowTitle("编辑浏览器" if self.is_edit_mode else "添加浏览器")
        self.setModal(True)
        self.resize(400, 150)
        
        self.setup_ui()
        
        if self.is_edit_mode:
            self.load_data()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 表单布局
        form_layout = QFormLayout()
        
        # 浏览器 ID
        self.id_edit = QLineEdit()
        self.id_edit.setPlaceholderText("例如: browser_001")
        if self.is_edit_mode:
            self.id_edit.setReadOnly(True)  # 编辑模式下 ID 不可修改
        form_layout.addRow("浏览器 ID:", self.id_edit)
        
        # 浏览器名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("例如: 测试浏览器 1")
        form_layout.addRow("显示名称:", self.name_edit)
        
        layout.addLayout(form_layout)
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def load_data(self):
        """加载浏览器数据"""
        if self.browser_data:
            self.id_edit.setText(self.browser_data.get("id", ""))
            self.name_edit.setText(self.browser_data.get("name", ""))
    
    def get_data(self) -> Dict[str, Any]:
        """
        获取表单数据
        
        Returns:
            Dict[str, Any]: 浏览器数据
        """
        return {
            "id": self.id_edit.text().strip(),
            "name": self.name_edit.text().strip(),
            "enabled": self.browser_data.get("enabled", True) if self.is_edit_mode else True
        }
    
    def accept(self):
        """验证并接受对话框"""
        data = self.get_data()
        
        if not data["id"]:
            QMessageBox.warning(self, "错误", "请输入浏览器 ID")
            return
        
        if not data["name"]:
            QMessageBox.warning(self, "错误", "请输入显示名称")
            return
        
        super().accept()


class BrowserListItem(QWidget):
    """浏览器列表项控件"""
    
    def __init__(self, browser_data: Dict[str, Any]):
        """
        初始化列表项
        
        Args:
            browser_data: 浏览器数据
        """
        super().__init__()
        self.browser_data = browser_data
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 2, 5, 2)
        
        # 复选框
        self.checkbox = QCheckBox()
        self.checkbox.setChecked(self.browser_data.get("enabled", True))
        layout.addWidget(self.checkbox)
        
        # 浏览器信息
        info_label = QLabel(f"{self.browser_data.get('name', '未知')} ({self.browser_data.get('id', '未知')})")
        layout.addWidget(info_label)
        
        layout.addStretch()
    
    def is_enabled(self) -> bool:
        """
        检查是否启用
        
        Returns:
            bool: 是否启用
        """
        return self.checkbox.isChecked()
    
    def set_enabled(self, enabled: bool):
        """
        设置启用状态
        
        Args:
            enabled: 是否启用
        """
        self.checkbox.setChecked(enabled)
    
    def get_browser_data(self) -> Dict[str, Any]:
        """
        获取浏览器数据
        
        Returns:
            Dict[str, Any]: 浏览器数据
        """
        data = self.browser_data.copy()
        data["enabled"] = self.is_enabled()
        return data


class BrowserPanel(QGroupBox):
    """浏览器管理面板"""
    
    # 定义信号
    browsers_changed = pyqtSignal()  # 浏览器列表发生变化
    
    def __init__(self, parent=None):
        """
        初始化面板
        
        Args:
            parent: 父窗口
        """
        super().__init__("浏览器管理", parent)
        self.config_manager = ConfigManager()
        self.setup_ui()
        self.load_browsers()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 浏览器列表
        self.browser_list = QListWidget()
        self.browser_list.setMinimumHeight(200)
        layout.addWidget(self.browser_list)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        # 添加按钮
        self.add_button = QPushButton("添加")
        self.add_button.clicked.connect(self.add_browser)
        button_layout.addWidget(self.add_button)
        
        # 编辑按钮
        self.edit_button = QPushButton("编辑")
        self.edit_button.clicked.connect(self.edit_browser)
        button_layout.addWidget(self.edit_button)
        
        # 删除按钮
        self.delete_button = QPushButton("删除")
        self.delete_button.clicked.connect(self.delete_browser)
        button_layout.addWidget(self.delete_button)
        
        # 刷新按钮
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.clicked.connect(self.load_browsers)
        button_layout.addWidget(self.refresh_button)
        
        layout.addLayout(button_layout)
        
        # 全选/全不选按钮
        select_layout = QHBoxLayout()
        
        self.select_all_button = QPushButton("全选")
        self.select_all_button.clicked.connect(self.select_all)
        select_layout.addWidget(self.select_all_button)
        
        self.select_none_button = QPushButton("全不选")
        self.select_none_button.clicked.connect(self.select_none)
        select_layout.addWidget(self.select_none_button)
        
        layout.addLayout(select_layout)
        
        # 连接信号
        self.browser_list.itemSelectionChanged.connect(self.update_button_states)
        self.update_button_states()
    
    def load_browsers(self):
        """加载浏览器列表"""
        try:
            browsers = self.config_manager.get_browsers()
            
            # 清空列表
            self.browser_list.clear()
            
            # 添加浏览器项
            for browser_data in browsers:
                self.add_browser_item(browser_data)
            
            logger.info(f"加载了 {len(browsers)} 个浏览器配置")
            
        except Exception as e:
            logger.error(f"加载浏览器列表失败: {e}")
            QMessageBox.critical(self, "错误", f"加载浏览器列表失败: {e}")
    
    def add_browser_item(self, browser_data: Dict[str, Any]):
        """
        添加浏览器列表项
        
        Args:
            browser_data: 浏览器数据
        """
        # 创建列表项
        item = QListWidgetItem()
        self.browser_list.addItem(item)
        
        # 创建自定义控件
        widget = BrowserListItem(browser_data)
        item.setSizeHint(widget.sizeHint())
        
        # 设置控件到列表项
        self.browser_list.setItemWidget(item, widget)
    
    def add_browser(self):
        """添加新浏览器"""
        dialog = BrowserDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            data = dialog.get_data()
            
            # 检查 ID 是否已存在
            if self.is_browser_id_exists(data["id"]):
                QMessageBox.warning(self, "错误", f"浏览器 ID '{data['id']}' 已存在")
                return
            
            # 添加到配置
            if self.config_manager.add_browser(data["id"], data["name"], data["enabled"]):
                self.load_browsers()
                self.browsers_changed.emit()
                logger.info(f"添加浏览器: {data['name']} ({data['id']})")
            else:
                QMessageBox.critical(self, "错误", "添加浏览器失败")
    
    def edit_browser(self):
        """编辑选中的浏览器"""
        current_item = self.browser_list.currentItem()
        if not current_item:
            return
        
        widget = self.browser_list.itemWidget(current_item)
        if not widget:
            return
        
        browser_data = widget.get_browser_data()
        dialog = BrowserDialog(self, browser_data)
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            new_data = dialog.get_data()
            
            # 更新配置
            self.update_browser_config(browser_data["id"], new_data)
            self.load_browsers()
            self.browsers_changed.emit()
            logger.info(f"编辑浏览器: {new_data['name']} ({new_data['id']})")
    
    def delete_browser(self):
        """删除选中的浏览器"""
        current_item = self.browser_list.currentItem()
        if not current_item:
            return
        
        widget = self.browser_list.itemWidget(current_item)
        if not widget:
            return
        
        browser_data = widget.get_browser_data()
        
        # 确认删除
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除浏览器 '{browser_data['name']}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            if self.config_manager.remove_browser(browser_data["id"]):
                self.load_browsers()
                self.browsers_changed.emit()
                logger.info(f"删除浏览器: {browser_data['name']} ({browser_data['id']})")
            else:
                QMessageBox.critical(self, "错误", "删除浏览器失败")
    
    def select_all(self):
        """全选浏览器"""
        for i in range(self.browser_list.count()):
            item = self.browser_list.item(i)
            widget = self.browser_list.itemWidget(item)
            if widget:
                widget.set_enabled(True)
    
    def select_none(self):
        """全不选浏览器"""
        for i in range(self.browser_list.count()):
            item = self.browser_list.item(i)
            widget = self.browser_list.itemWidget(item)
            if widget:
                widget.set_enabled(False)
    
    def update_button_states(self):
        """更新按钮状态"""
        has_selection = self.browser_list.currentItem() is not None
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
    
    def get_enabled_browsers(self) -> List[Dict[str, Any]]:
        """
        获取启用的浏览器列表
        
        Returns:
            List[Dict[str, Any]]: 启用的浏览器列表
        """
        enabled_browsers = []
        
        for i in range(self.browser_list.count()):
            item = self.browser_list.item(i)
            widget = self.browser_list.itemWidget(item)
            if widget and widget.is_enabled():
                enabled_browsers.append(widget.get_browser_data())
        
        return enabled_browsers
    
    def is_browser_id_exists(self, browser_id: str) -> bool:
        """
        检查浏览器 ID 是否已存在
        
        Args:
            browser_id: 浏览器 ID
            
        Returns:
            bool: 是否存在
        """
        browsers = self.config_manager.get_browsers()
        return any(b.get("id") == browser_id for b in browsers)
    
    def update_browser_config(self, old_id: str, new_data: Dict[str, Any]):
        """
        更新浏览器配置
        
        Args:
            old_id: 原浏览器 ID
            new_data: 新数据
        """
        # 删除旧配置
        self.config_manager.remove_browser(old_id)
        # 添加新配置
        self.config_manager.add_browser(
            new_data["id"], 
            new_data["name"], 
            new_data["enabled"]
        )
