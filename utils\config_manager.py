"""
配置管理工具
用于加载和保存应用程序配置
"""

import json
import os
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self.config_file = self.config_dir / "config.json"
        self.xpath_config_file = self.config_dir / "xpath_config.json"
        
        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)
        
        self._config_cache = {}
        self._xpath_cache = {}
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载主配置文件
        
        Returns:
            Dict[str, Any]: 配置数据
        """
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self._config_cache = json.load(f)
                    logger.info("配置文件加载成功")
            else:
                logger.warning("配置文件不存在，使用默认配置")
                self._config_cache = self._get_default_config()
                self.save_config(self._config_cache)
                
            return self._config_cache.copy()
            
        except json.JSONDecodeError as e:
            logger.error(f"配置文件格式错误: {e}")
            return self._get_default_config()
        except Exception as e:
            logger.error(f"加载配置文件时发生错误: {e}")
            return self._get_default_config()
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """
        保存主配置文件
        
        Args:
            config: 配置数据
            
        Returns:
            bool: 是否保存成功
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            
            self._config_cache = config.copy()
            logger.info("配置文件保存成功")
            return True
            
        except Exception as e:
            logger.error(f"保存配置文件时发生错误: {e}")
            return False
    
    def load_xpath_config(self) -> Dict[str, Any]:
        """
        加载 XPath 配置文件
        
        Returns:
            Dict[str, Any]: XPath 配置数据
        """
        try:
            if self.xpath_config_file.exists():
                with open(self.xpath_config_file, 'r', encoding='utf-8') as f:
                    self._xpath_cache = json.load(f)
                    logger.info("XPath 配置文件加载成功")
            else:
                logger.warning("XPath 配置文件不存在，使用默认配置")
                self._xpath_cache = self._get_default_xpath_config()
                self.save_xpath_config(self._xpath_cache)
                
            return self._xpath_cache.copy()
            
        except json.JSONDecodeError as e:
            logger.error(f"XPath 配置文件格式错误: {e}")
            return self._get_default_xpath_config()
        except Exception as e:
            logger.error(f"加载 XPath 配置文件时发生错误: {e}")
            return self._get_default_xpath_config()
    
    def save_xpath_config(self, config: Dict[str, Any]) -> bool:
        """
        保存 XPath 配置文件
        
        Args:
            config: XPath 配置数据
            
        Returns:
            bool: 是否保存成功
        """
        try:
            with open(self.xpath_config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            
            self._xpath_cache = config.copy()
            logger.info("XPath 配置文件保存成功")
            return True
            
        except Exception as e:
            logger.error(f"保存 XPath 配置文件时发生错误: {e}")
            return False
    
    def get_browsers(self) -> List[Dict[str, Any]]:
        """
        获取浏览器配置列表
        
        Returns:
            List[Dict[str, Any]]: 浏览器配置列表
        """
        config = self.load_config()
        return config.get("browsers", [])
    
    def add_browser(self, browser_id: str, name: str, enabled: bool = True) -> bool:
        """
        添加浏览器配置
        
        Args:
            browser_id: 浏览器 ID
            name: 浏览器名称
            enabled: 是否启用
            
        Returns:
            bool: 是否添加成功
        """
        config = self.load_config()
        browsers = config.get("browsers", [])
        
        # 检查是否已存在
        for browser in browsers:
            if browser.get("id") == browser_id:
                logger.warning(f"浏览器 ID {browser_id} 已存在")
                return False
        
        # 添加新浏览器
        browsers.append({
            "id": browser_id,
            "name": name,
            "enabled": enabled
        })
        
        config["browsers"] = browsers
        return self.save_config(config)
    
    def remove_browser(self, browser_id: str) -> bool:
        """
        删除浏览器配置
        
        Args:
            browser_id: 浏览器 ID
            
        Returns:
            bool: 是否删除成功
        """
        config = self.load_config()
        browsers = config.get("browsers", [])
        
        # 查找并删除
        original_count = len(browsers)
        browsers = [b for b in browsers if b.get("id") != browser_id]
        
        if len(browsers) < original_count:
            config["browsers"] = browsers
            return self.save_config(config)
        else:
            logger.warning(f"未找到浏览器 ID: {browser_id}")
            return False
    
    def get_settings(self) -> Dict[str, Any]:
        """
        获取应用程序设置
        
        Returns:
            Dict[str, Any]: 设置数据
        """
        config = self.load_config()
        return config.get("settings", {})
    
    def update_settings(self, settings: Dict[str, Any]) -> bool:
        """
        更新应用程序设置
        
        Args:
            settings: 新的设置数据
            
        Returns:
            bool: 是否更新成功
        """
        config = self.load_config()
        config["settings"] = settings
        return self.save_config(config)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "browsers": [],
            "settings": {
                "min_watch_time": 3,
                "max_watch_time": 8,
                "like_probability": 0.3,
                "subscribe_probability": 0.1,
                "scroll_count": 50
            },
            "api": {
                "base_url": "http://127.0.0.1:53200",
                "timeout": 30
            }
        }
    
    def _get_default_xpath_config(self) -> Dict[str, Any]:
        """获取默认 XPath 配置"""
        return {
            "selectors": {
                "like_button": [
                    "button[aria-label*='like']",
                    "button[aria-label*='赞']",
                    "#like-button",
                    "ytd-toggle-button-renderer:first-child button"
                ],
                "subscribe_button": [
                    "button[aria-label*='Subscribe']",
                    "button[aria-label*='订阅']",
                    "#subscribe-button",
                    "ytd-subscribe-button-renderer button"
                ],
                "video_container": [
                    "ytd-shorts",
                    "#shorts-container",
                    ".ytd-shorts"
                ]
            },
            "wait_times": {
                "element_load": 3,
                "action_delay": 1,
                "scroll_delay": 2
            }
        }
