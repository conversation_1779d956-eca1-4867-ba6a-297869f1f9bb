#!/usr/bin/env python3
"""
基本功能测试脚本
用于验证应用程序的核心组件是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        # 测试核心模块
        from src.browser_api import ixBrowserAPI
        from src.youtube_bot import YouTubeBot
        from src.bot_worker import BotWorker, BotManager
        print("✓ 核心模块导入成功")
        
        # 测试 GUI 模块
        from src.gui.browser_panel import BrowserPanel
        from src.gui.config_panel import ConfigPanel
        from src.gui.status_panel import StatusPanel
        from src.gui.log_panel import LogPanel
        print("✓ GUI 模块导入成功")
        
        # 测试工具模块
        from utils.config_manager import ConfigManager
        from utils.logger_setup import LoggerSetup
        print("✓ 工具模块导入成功")
        
        # 测试主窗口
        from src.main_window import MainWindow
        print("✓ 主窗口模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return False


def test_config_manager():
    """测试配置管理器"""
    print("\n测试配置管理器...")
    
    try:
        from utils.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        
        # 测试加载配置
        config = config_manager.load_config()
        print(f"✓ 配置加载成功: {len(config)} 个配置项")
        
        # 测试获取设置
        settings = config_manager.get_settings()
        print(f"✓ 设置获取成功: {len(settings)} 个设置项")
        
        # 测试获取浏览器列表
        browsers = config_manager.get_browsers()
        print(f"✓ 浏览器列表获取成功: {len(browsers)} 个浏览器")
        
        # 测试添加浏览器
        test_id = "test_browser_001"
        if config_manager.add_browser(test_id, "测试浏览器", True):
            print("✓ 浏览器添加成功")
            
            # 测试删除浏览器
            if config_manager.remove_browser(test_id):
                print("✓ 浏览器删除成功")
            else:
                print("✗ 浏览器删除失败")
        else:
            print("✗ 浏览器添加失败")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置管理器测试失败: {e}")
        return False


def test_browser_api():
    """测试浏览器 API"""
    print("\n测试浏览器 API...")
    
    try:
        from src.browser_api import ixBrowserAPI
        
        api = ixBrowserAPI()
        
        # 测试 API 服务检查
        is_available = api.check_api_server()
        if is_available:
            print("✓ ixBrowser API 服务可用")
            
            # 测试获取浏览器列表
            browser_list = api.get_browser_list()
            if browser_list is not None:
                print("✓ 浏览器列表获取成功")
            else:
                print("⚠ 浏览器列表获取失败（可能是正常的）")
        else:
            print("⚠ ixBrowser API 服务不可用（这是正常的，如果 ixBrowser 未运行）")
        
        return True
        
    except Exception as e:
        print(f"✗ 浏览器 API 测试失败: {e}")
        return False


def test_gui_components():
    """测试 GUI 组件"""
    print("\n测试 GUI 组件...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        import sys
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 测试各个面板
        from src.gui.browser_panel import BrowserPanel
        browser_panel = BrowserPanel()
        print("✓ 浏览器面板创建成功")
        
        from src.gui.config_panel import ConfigPanel
        config_panel = ConfigPanel()
        print("✓ 配置面板创建成功")
        
        from src.gui.status_panel import StatusPanel
        status_panel = StatusPanel()
        print("✓ 状态面板创建成功")
        
        from src.gui.log_panel import LogPanel
        log_panel = LogPanel()
        print("✓ 日志面板创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ GUI 组件测试失败: {e}")
        return False


def test_logger_setup():
    """测试日志设置"""
    print("\n测试日志设置...")
    
    try:
        from utils.logger_setup import LoggerSetup
        import logging
        
        logger_setup = LoggerSetup()
        
        # 测试日志设置
        qt_handler = logger_setup.setup_logging(level=logging.INFO)
        print("✓ 日志系统设置成功")
        
        # 测试日志记录
        logger = logging.getLogger("test")
        logger.info("这是一条测试日志消息")
        logger.warning("这是一条测试警告消息")
        print("✓ 日志记录测试成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 日志设置测试失败: {e}")
        return False


def test_file_structure():
    """测试文件结构"""
    print("\n测试文件结构...")
    
    required_files = [
        "main.py",
        "requirements.txt",
        "config/config.json",
        "config/xpath_config.json",
        "src/__init__.py",
        "src/browser_api.py",
        "src/youtube_bot.py",
        "src/bot_worker.py",
        "src/main_window.py",
        "src/gui/__init__.py",
        "src/gui/browser_panel.py",
        "src/gui/config_panel.py",
        "src/gui/status_panel.py",
        "src/gui/log_panel.py",
        "utils/__init__.py",
        "utils/config_manager.py",
        "utils/logger_setup.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"✗ 缺少文件: {missing_files}")
        return False
    else:
        print(f"✓ 所有必需文件都存在 ({len(required_files)} 个文件)")
        return True


def main():
    """主测试函数"""
    print("YouTube Shorts 机器人 - 基本功能测试")
    print("=" * 50)
    
    tests = [
        ("文件结构", test_file_structure),
        ("模块导入", test_imports),
        ("配置管理器", test_config_manager),
        ("浏览器 API", test_browser_api),
        ("日志设置", test_logger_setup),
        ("GUI 组件", test_gui_components),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{passed + 1}/{total}] {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！应用程序基本功能正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关组件。")
        return 1


if __name__ == "__main__":
    exit_code = main()
    
    print("\n按 Enter 键退出...")
    input()
    
    sys.exit(exit_code)
