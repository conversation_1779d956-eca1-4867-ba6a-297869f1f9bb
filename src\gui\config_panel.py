"""
配置面板
用于设置机器人运行参数
"""

import logging
from typing import Dict, Any
from PyQt6.QtWidgets import (
    QGroupBox, QVBoxLayout, QHBoxLayout, QFormLayout,
    QSpinBox, QDoubleSpinBox, QPushButton, QLabel,
    QMessageBox, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from utils.config_manager import ConfigManager

logger = logging.getLogger(__name__)


class ConfigPanel(QGroupBox):
    """配置面板"""
    
    # 定义信号
    settings_changed = pyqtSignal(dict)  # 设置发生变化
    
    def __init__(self, parent=None):
        """
        初始化面板
        
        Args:
            parent: 父窗口
        """
        super().__init__("运行配置", parent)
        self.config_manager = ConfigManager()
        self.setup_ui()
        self.load_settings()
        self.connect_signals()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建表单布局
        form_layout = QFormLayout()
        
        # 观看时间设置
        watch_time_frame = QFrame()
        watch_time_layout = QHBoxLayout(watch_time_frame)
        watch_time_layout.setContentsMargins(0, 0, 0, 0)
        
        self.min_watch_time_spin = QSpinBox()
        self.min_watch_time_spin.setRange(1, 60)
        self.min_watch_time_spin.setSuffix(" 秒")
        self.min_watch_time_spin.setToolTip("最小观看时间")
        watch_time_layout.addWidget(self.min_watch_time_spin)
        
        watch_time_layout.addWidget(QLabel(" 到 "))
        
        self.max_watch_time_spin = QSpinBox()
        self.max_watch_time_spin.setRange(1, 120)
        self.max_watch_time_spin.setSuffix(" 秒")
        self.max_watch_time_spin.setToolTip("最大观看时间")
        watch_time_layout.addWidget(self.max_watch_time_spin)
        
        form_layout.addRow("观看时间:", watch_time_frame)
        
        # 点赞概率
        self.like_probability_spin = QDoubleSpinBox()
        self.like_probability_spin.setRange(0.0, 1.0)
        self.like_probability_spin.setSingleStep(0.1)
        self.like_probability_spin.setDecimals(2)
        self.like_probability_spin.setSuffix(" (0-1)")
        self.like_probability_spin.setToolTip("点赞概率，0表示从不点赞，1表示总是点赞")
        form_layout.addRow("点赞概率:", self.like_probability_spin)
        
        # 订阅概率
        self.subscribe_probability_spin = QDoubleSpinBox()
        self.subscribe_probability_spin.setRange(0.0, 1.0)
        self.subscribe_probability_spin.setSingleStep(0.1)
        self.subscribe_probability_spin.setDecimals(2)
        self.subscribe_probability_spin.setSuffix(" (0-1)")
        self.subscribe_probability_spin.setToolTip("订阅概率，0表示从不订阅，1表示总是订阅")
        form_layout.addRow("订阅概率:", self.subscribe_probability_spin)
        
        # 视频数量
        self.scroll_count_spin = QSpinBox()
        self.scroll_count_spin.setRange(1, 1000)
        self.scroll_count_spin.setSuffix(" 个")
        self.scroll_count_spin.setToolTip("每个浏览器要观看的视频数量")
        form_layout.addRow("视频数量:", self.scroll_count_spin)
        
        layout.addLayout(form_layout)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(line)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        # 保存按钮
        self.save_button = QPushButton("保存配置")
        self.save_button.clicked.connect(self.save_settings)
        button_layout.addWidget(self.save_button)
        
        # 重置按钮
        self.reset_button = QPushButton("重置默认")
        self.reset_button.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(self.reset_button)
        
        # 刷新按钮
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.clicked.connect(self.load_settings)
        button_layout.addWidget(self.refresh_button)
        
        layout.addLayout(button_layout)
        
        # 状态标签
        self.status_label = QLabel("配置已加载")
        self.status_label.setStyleSheet("color: green;")
        layout.addWidget(self.status_label)
        
        # 添加说明文本
        help_label = QLabel(
            "提示：\n"
            "• 观看时间：每个视频的观看时长范围\n"
            "• 点赞概率：随机点赞的概率（0.3 = 30%）\n"
            "• 订阅概率：随机订阅的概率（0.1 = 10%）\n"
            "• 视频数量：每个浏览器处理的视频总数"
        )
        help_label.setStyleSheet("color: gray; font-size: 10px;")
        help_label.setWordWrap(True)
        layout.addWidget(help_label)
    
    def connect_signals(self):
        """连接信号"""
        # 当任何设置改变时发出信号
        self.min_watch_time_spin.valueChanged.connect(self.on_settings_changed)
        self.max_watch_time_spin.valueChanged.connect(self.on_settings_changed)
        self.like_probability_spin.valueChanged.connect(self.on_settings_changed)
        self.subscribe_probability_spin.valueChanged.connect(self.on_settings_changed)
        self.scroll_count_spin.valueChanged.connect(self.on_settings_changed)
        
        # 确保最小值不大于最大值
        self.min_watch_time_spin.valueChanged.connect(self.validate_watch_time)
        self.max_watch_time_spin.valueChanged.connect(self.validate_watch_time)
    
    def load_settings(self):
        """加载设置"""
        try:
            settings = self.config_manager.get_settings()
            
            # 设置控件值
            self.min_watch_time_spin.setValue(settings.get("min_watch_time", 3))
            self.max_watch_time_spin.setValue(settings.get("max_watch_time", 8))
            self.like_probability_spin.setValue(settings.get("like_probability", 0.3))
            self.subscribe_probability_spin.setValue(settings.get("subscribe_probability", 0.1))
            self.scroll_count_spin.setValue(settings.get("scroll_count", 50))
            
            self.status_label.setText("配置已加载")
            self.status_label.setStyleSheet("color: green;")
            
            logger.info("配置设置加载成功")
            
        except Exception as e:
            logger.error(f"加载配置设置失败: {e}")
            self.status_label.setText(f"加载失败: {e}")
            self.status_label.setStyleSheet("color: red;")
            QMessageBox.critical(self, "错误", f"加载配置设置失败: {e}")
    
    def save_settings(self):
        """保存设置"""
        try:
            settings = self.get_current_settings()
            
            if self.config_manager.update_settings(settings):
                self.status_label.setText("配置已保存")
                self.status_label.setStyleSheet("color: green;")
                self.settings_changed.emit(settings)
                logger.info("配置设置保存成功")
            else:
                self.status_label.setText("保存失败")
                self.status_label.setStyleSheet("color: red;")
                QMessageBox.critical(self, "错误", "保存配置设置失败")
                
        except Exception as e:
            logger.error(f"保存配置设置失败: {e}")
            self.status_label.setText(f"保存失败: {e}")
            self.status_label.setStyleSheet("color: red;")
            QMessageBox.critical(self, "错误", f"保存配置设置失败: {e}")
    
    def reset_to_defaults(self):
        """重置为默认设置"""
        reply = QMessageBox.question(
            self, "确认重置", 
            "确定要重置为默认设置吗？这将覆盖当前配置。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # 设置默认值
            self.min_watch_time_spin.setValue(3)
            self.max_watch_time_spin.setValue(8)
            self.like_probability_spin.setValue(0.3)
            self.subscribe_probability_spin.setValue(0.1)
            self.scroll_count_spin.setValue(50)
            
            # 保存默认设置
            self.save_settings()
            
            logger.info("配置已重置为默认值")
    
    def get_current_settings(self) -> Dict[str, Any]:
        """
        获取当前设置
        
        Returns:
            Dict[str, Any]: 当前设置
        """
        return {
            "min_watch_time": self.min_watch_time_spin.value(),
            "max_watch_time": self.max_watch_time_spin.value(),
            "like_probability": self.like_probability_spin.value(),
            "subscribe_probability": self.subscribe_probability_spin.value(),
            "scroll_count": self.scroll_count_spin.value()
        }
    
    def validate_watch_time(self):
        """验证观看时间设置"""
        min_time = self.min_watch_time_spin.value()
        max_time = self.max_watch_time_spin.value()
        
        if min_time > max_time:
            # 自动调整
            if self.sender() == self.min_watch_time_spin:
                self.max_watch_time_spin.setValue(min_time)
            else:
                self.min_watch_time_spin.setValue(max_time)
    
    def on_settings_changed(self):
        """设置改变时的处理"""
        self.status_label.setText("配置已修改（未保存）")
        self.status_label.setStyleSheet("color: orange;")
    
    def is_valid_configuration(self) -> bool:
        """
        检查当前配置是否有效
        
        Returns:
            bool: 配置是否有效
        """
        settings = self.get_current_settings()
        
        # 检查观看时间
        if settings["min_watch_time"] > settings["max_watch_time"]:
            return False
        
        # 检查概率值
        if not (0 <= settings["like_probability"] <= 1):
            return False
        
        if not (0 <= settings["subscribe_probability"] <= 1):
            return False
        
        # 检查视频数量
        if settings["scroll_count"] <= 0:
            return False
        
        return True
    
    def get_validation_errors(self) -> List[str]:
        """
        获取配置验证错误
        
        Returns:
            List[str]: 错误列表
        """
        errors = []
        settings = self.get_current_settings()
        
        if settings["min_watch_time"] > settings["max_watch_time"]:
            errors.append("最小观看时间不能大于最大观看时间")
        
        if not (0 <= settings["like_probability"] <= 1):
            errors.append("点赞概率必须在 0 到 1 之间")
        
        if not (0 <= settings["subscribe_probability"] <= 1):
            errors.append("订阅概率必须在 0 到 1 之间")
        
        if settings["scroll_count"] <= 0:
            errors.append("视频数量必须大于 0")
        
        return errors
    
    def auto_save_enabled(self) -> bool:
        """
        检查是否启用自动保存
        
        Returns:
            bool: 是否启用自动保存
        """
        # 可以在这里添加自动保存逻辑
        return False
