#!/usr/bin/env python3
"""
YouTube Shorts 机器人应用程序入口点
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPixmap, QFont

from src.main_window import MainWindow
from utils.logger_setup import logger_setup
from utils.config_manager import ConfigManager


def check_dependencies():
    """检查必要的依赖项"""
    missing_deps = []
    
    try:
        import PyQt6
    except ImportError:
        missing_deps.append("PyQt6")
    
    try:
        import playwright
    except ImportError:
        missing_deps.append("playwright")
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    if missing_deps:
        return False, missing_deps
    
    return True, []


def setup_application():
    """设置应用程序"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("YouTube Shorts 机器人")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("YouTube Bot")
    app.setOrganizationDomain("youtube-bot.local")
    
    # 设置应用程序样式
    app.setStyle("Fusion")
    
    # 设置全局字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    return app


def create_splash_screen():
    """创建启动画面"""
    # 创建一个简单的启动画面
    splash_pix = QPixmap(400, 300)
    splash_pix.fill(Qt.GlobalColor.white)
    
    splash = QSplashScreen(splash_pix, Qt.WindowType.WindowStaysOnTopHint)
    splash.setMask(splash_pix.mask())
    
    # 显示启动信息
    splash.show()
    splash.showMessage(
        "YouTube Shorts 机器人\n正在启动...", 
        Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignBottom,
        Qt.GlobalColor.black
    )
    
    return splash


def initialize_logging():
    """初始化日志系统"""
    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 设置日志文件
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d")
    log_file = log_dir / f"youtube_bot_{timestamp}.log"
    
    # 初始化日志系统
    logger_setup.setup_logging(
        level=logging.INFO,
        log_file=str(log_file)
    )
    
    logger = logging.getLogger(__name__)
    logger.info("=" * 50)
    logger.info("YouTube Shorts 机器人应用程序启动")
    logger.info("=" * 50)
    
    return logger


def check_playwright_browsers():
    """检查 Playwright 浏览器"""
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            # 检查 Chromium 是否可用
            try:
                browser = p.chromium.launch(headless=True)
                browser.close()
                return True
            except Exception:
                return False
    except Exception:
        return False


def show_startup_checks(splash, logger):
    """显示启动检查"""
    checks = [
        ("检查依赖项...", check_dependencies),
        ("初始化配置...", lambda: (True, [])),
        ("检查 Playwright 浏览器...", lambda: (check_playwright_browsers(), [])),
    ]
    
    for i, (message, check_func) in enumerate(checks):
        splash.showMessage(
            f"YouTube Shorts 机器人\n{message}", 
            Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignBottom,
            Qt.GlobalColor.black
        )
        
        QApplication.processEvents()
        
        try:
            if hasattr(check_func, '__call__'):
                if check_func == check_dependencies:
                    success, missing = check_func()
                    if not success:
                        error_msg = f"缺少依赖项: {', '.join(missing)}\n请运行: pip install -r requirements.txt"
                        logger.error(error_msg)
                        QMessageBox.critical(None, "依赖项错误", error_msg)
                        return False
                elif check_func == lambda: (check_playwright_browsers(), []):
                    success = check_playwright_browsers()
                    if not success:
                        warning_msg = (
                            "Playwright 浏览器未安装或不可用。\n"
                            "请运行: playwright install chromium\n\n"
                            "应用程序仍可启动，但浏览器自动化功能可能无法正常工作。"
                        )
                        logger.warning(warning_msg)
                        QMessageBox.warning(None, "Playwright 警告", warning_msg)
                else:
                    check_func()
            
            logger.info(f"✓ {message}")
            
        except Exception as e:
            logger.error(f"✗ {message} - 错误: {e}")
            QMessageBox.critical(None, "启动错误", f"{message}\n错误: {e}")
            return False
        
        # 模拟加载时间
        QTimer.singleShot(500, lambda: None)
        QApplication.processEvents()
    
    return True


def main():
    """主函数"""
    try:
        # 设置应用程序
        app = setup_application()
        
        # 创建启动画面
        splash = create_splash_screen()
        
        # 初始化日志
        logger = initialize_logging()
        
        # 执行启动检查
        if not show_startup_checks(splash, logger):
            splash.close()
            return 1
        
        # 创建主窗口
        splash.showMessage(
            "YouTube Shorts 机器人\n正在加载主界面...", 
            Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignBottom,
            Qt.GlobalColor.black
        )
        QApplication.processEvents()
        
        main_window = MainWindow()
        
        # 显示主窗口
        main_window.show()
        
        # 关闭启动画面
        splash.finish(main_window)
        
        logger.info("应用程序启动完成")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        error_msg = f"应用程序启动失败: {e}"
        print(error_msg)
        
        # 尝试显示错误对话框
        try:
            if 'app' in locals():
                QMessageBox.critical(None, "启动错误", error_msg)
        except:
            pass
        
        return 1


if __name__ == "__main__":
    # 设置异常处理
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        logger = logging.getLogger(__name__)
        logger.critical(
            "未捕获的异常", 
            exc_info=(exc_type, exc_value, exc_traceback)
        )
    
    sys.excepthook = handle_exception
    
    # 运行应用程序
    exit_code = main()
    sys.exit(exit_code)
